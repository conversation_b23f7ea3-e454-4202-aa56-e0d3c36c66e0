using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using TDA4_Emulator.ViewModels;
using TDA4_Emulator.Views;
using System.Threading.Tasks;

namespace TDA4_Emulator;

public partial class App : Application
{
    private MainWindowViewModel? _mainViewModel;

    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
    }

    public override void OnFrameworkInitializationCompleted()
    {
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            _mainViewModel = new MainWindowViewModel();

            desktop.MainWindow = new MainWindow
            {
                DataContext = _mainViewModel,
            };

            // Handle application shutdown to ensure proper cleanup
            desktop.ShutdownRequested += OnShutdownRequested;
        }

        base.OnFrameworkInitializationCompleted();
    }

    private async void OnShutdownRequested(object? sender, ShutdownRequestedEventArgs e)
    {
        if (_mainViewModel != null)
        {
            try
            {
                // Cancel shutdown temporarily to perform cleanup
                e.Cancel = true;

                // Perform graceful shutdown
                await _mainViewModel.ShutdownAsync();

                // Now allow the application to shut down
                if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
                {
                    // Remove the event handler to avoid recursion
                    desktop.ShutdownRequested -= OnShutdownRequested;

                    // Shutdown the application
                    desktop.Shutdown();
                }
            }
            catch
            {
                // If shutdown fails, still allow the application to close
                // to prevent the application from hanging
                if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
                {
                    desktop.ShutdownRequested -= OnShutdownRequested;
                    desktop.Shutdown();
                }
            }
        }
    }
}