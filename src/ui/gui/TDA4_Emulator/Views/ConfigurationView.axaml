<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="using:TDA4_Emulator.ViewModels"
             x:Class="TDA4_Emulator.Views.ConfigurationView"
             x:DataType="vm:ConfigurationViewModel">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Spacing="8" Margin="0,0,0,24">
            <TextBlock Text="Step 2: Configuration" 
                       FontSize="20" FontWeight="Bold" 
                       Foreground="#800020"/>
            <TextBlock Text="Configure core binary paths and settings" 
                       FontSize="14" Foreground="#666666"/>
        </StackPanel>

        <!-- Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="24"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Binary Paths Panel -->
            <ScrollViewer Grid.Column="0">
                <StackPanel Spacing="16">
                    <Label Content="Input text + browse button for 14 core binary path, 4 groups" 
                           Classes="section" FontSize="16"/>

                    <!-- ARM A72 Group -->
                    <StackPanel Spacing="8">
                        <Label Content="ARM A72" FontWeight="Bold" Foreground="#800020"/>
                        <ItemsControl ItemsSource="{Binding A72Cores}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Label Grid.Column="0" Content="{Binding DisplayName}" 
                                               VerticalAlignment="Center"/>
                                        <TextBox Grid.Column="1" Text="{Binding BinaryPath}"
                                                 Classes="path" Watermark="Select binary executable..."/>
                                        <TextBlock Grid.Column="2" Text="✓" Foreground="Green" FontWeight="Bold"
                                                   VerticalAlignment="Center" Margin="4,0"
                                                   IsVisible="{Binding IsPathValid}"/>
                                        <Button Grid.Column="3" Content="Browse" Classes="secondary"
                                                Command="{Binding $parent[UserControl].DataContext.BrowseBinaryCommand}"
                                                CommandParameter="{Binding CoreType}"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        <Separator Background="#CCCCCC" Height="1" Margin="0,8"/>
                    </StackPanel>

                    <!-- ARM MCU R5F Group -->
                    <StackPanel Spacing="8">
                        <Label Content="ARM MCU R5F" FontWeight="Bold" Foreground="#800020"/>
                        <ItemsControl ItemsSource="{Binding McuR5FCores}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Label Grid.Column="0" Content="{Binding DisplayName}" 
                                               VerticalAlignment="Center"/>
                                        <TextBox Grid.Column="1" Text="{Binding BinaryPath}"
                                                 Classes="path" Watermark="Select binary executable..."/>
                                        <TextBlock Grid.Column="2" Text="✓" Foreground="Green" FontWeight="Bold"
                                                   VerticalAlignment="Center" Margin="4,0"
                                                   IsVisible="{Binding IsPathValid}"/>
                                        <Button Grid.Column="3" Content="Browse" Classes="secondary"
                                                Command="{Binding $parent[UserControl].DataContext.BrowseBinaryCommand}"
                                                CommandParameter="{Binding CoreType}"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        <Separator Background="#CCCCCC" Height="1" Margin="0,8"/>
                    </StackPanel>

                    <!-- ARM Main R5F Group -->
                    <StackPanel Spacing="8">
                        <Label Content="ARM Main R5F" FontWeight="Bold" Foreground="#800020"/>
                        <ItemsControl ItemsSource="{Binding MainR5FCores}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Label Grid.Column="0" Content="{Binding DisplayName}" 
                                               VerticalAlignment="Center"/>
                                        <TextBox Grid.Column="1" Text="{Binding BinaryPath}"
                                                 Classes="path" Watermark="Select binary executable..."/>
                                        <TextBlock Grid.Column="2" Text="✓" Foreground="Green" FontWeight="Bold"
                                                   VerticalAlignment="Center" Margin="4,0"
                                                   IsVisible="{Binding IsPathValid}"/>
                                        <Button Grid.Column="3" Content="Browse" Classes="secondary"
                                                Command="{Binding $parent[UserControl].DataContext.BrowseBinaryCommand}"
                                                CommandParameter="{Binding CoreType}"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        <Separator Background="#CCCCCC" Height="1" Margin="0,8"/>
                    </StackPanel>

                    <!-- DSP C7x Group -->
                    <StackPanel Spacing="8">
                        <Label Content="DSP C7x" FontWeight="Bold" Foreground="#800020"/>
                        <ItemsControl ItemsSource="{Binding C7xCores}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,4">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Label Grid.Column="0" Content="{Binding DisplayName}" 
                                               VerticalAlignment="Center"/>
                                        <TextBox Grid.Column="1" Text="{Binding BinaryPath}"
                                                 Classes="path" Watermark="Select binary executable..."/>
                                        <TextBlock Grid.Column="2" Text="✓" Foreground="Green" FontWeight="Bold"
                                                   VerticalAlignment="Center" Margin="4,0"
                                                   IsVisible="{Binding IsPathValid}"/>
                                        <Button Grid.Column="3" Content="Browse" Classes="secondary"
                                                Command="{Binding $parent[UserControl].DataContext.BrowseBinaryCommand}"
                                                CommandParameter="{Binding CoreType}"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>

            <!-- Settings Panel -->
            <StackPanel Grid.Column="2" Spacing="16">
                <Label Content="Settings" Classes="section" FontSize="16"/>
                
                <Border BorderBrush="#CCCCCC" BorderThickness="1" 
                        CornerRadius="4" Padding="16" Background="White">
                    <StackPanel Spacing="16">
                        
                        <!-- QEMU Binary Path -->
                        <StackPanel Spacing="4">
                            <Label Content="qemuBinaryPath input text and browse button"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" Text="{Binding QemuBinaryPath}"
                                         Classes="path" Watermark="Select QEMU binary..."/>
                                <Button Grid.Column="1" Content="Browse" Classes="secondary"
                                        Command="{Binding BrowseQemuBinaryCommand}"/>
                            </Grid>
                            <TextBlock Text="{Binding QemuPathError}" Foreground="Red"
                                       FontSize="11" IsVisible="{Binding QemuPathError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                        </StackPanel>

                        <!-- Auto Save Configuration -->
                        <CheckBox Content="autoSaveConfiguration check box"
                                  IsChecked="{Binding AutoSaveConfiguration}"/>

                        <!-- Process Timeout -->
                        <StackPanel Spacing="4">
                            <Label Content="processTimeoutSeconds input field"/>
                            <NumericUpDown Value="{Binding ProcessTimeoutSeconds}"
                                           Minimum="1" Maximum="3600"
                                           Increment="1" FormatString="0"/>
                        </StackPanel>

                        <!-- Configuration Buttons -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Button Grid.Column="0" Content="Reset configuration" Classes="secondary"
                                    Command="{Binding ResetConfigurationCommand}"
                                    Margin="0,0,4,0"/>
                            <Button Grid.Column="1" Content="Load configuration" Classes="secondary"
                                    Command="{Binding LoadConfigurationCommand}"
                                    Margin="4,0,0,0"/>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Grid>

        <!-- Navigation -->
        <Grid Grid.Row="2" Margin="0,24,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Back Button -->
            <Button Grid.Column="0" Content="Back" Classes="secondary"
                    Command="{Binding BackCommand}"
                    MinWidth="100" Padding="16,8"/>

            <!-- Validation Status -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" 
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center" Spacing="8">
                <Ellipse Width="12" Height="12" 
                         Fill="{Binding CanNavigateNext, Converter={StaticResource BoolToColorConverter}}"/>
                <TextBlock Text="{Binding CanNavigateNext, StringFormat='Step Valid: {0}'}"
                           VerticalAlignment="Center" FontSize="12"/>
            </StackPanel>

            <!-- Next Button -->
            <Button Grid.Column="2" Content="Next" Classes="primary"
                    Command="{Binding NextCommand}"
                    IsEnabled="{Binding CanNavigateNext}"
                    MinWidth="100" Padding="16,8"/>
        </Grid>
    </Grid>

</UserControl>
