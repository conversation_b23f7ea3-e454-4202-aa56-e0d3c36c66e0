<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="using:TDA4_Emulator.ViewModels"
             xmlns:models="using:TDA4_Emulator.Models"
             xmlns:converters="using:TDA4_Emulator.Converters"
             x:Class="TDA4_Emulator.Views.ModelSelectionView"
             x:DataType="vm:ModelSelectionViewModel">

    <UserControl.Resources>
        <converters:TDA4ModelToDisplayNameConverter x:Key="TDA4ModelToDisplayNameConverter"/>
        <converters:TestModeToDisplayNameConverter x:Key="TestModeToDisplayNameConverter"/>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
    </UserControl.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Spacing="8" Margin="0,0,0,24">
            <TextBlock Text="Step 1: Model Selection" 
                       FontSize="20" FontWeight="Bold" 
                       Foreground="#800020"/>
            <TextBlock Text="Select TDA4 model and test mode for emulation" 
                       FontSize="14" Foreground="#666666"/>
        </StackPanel>

        <!-- Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="24"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Model Selection -->
            <StackPanel Grid.Column="0" Spacing="16">
                <Label Content="Model list (Dropdown list)" 
                       Classes="section" FontSize="16"/>
                
                <Border BorderBrush="#CCCCCC" BorderThickness="1" 
                        CornerRadius="4" Padding="16" Background="White">
                    <StackPanel Spacing="12">
                        <ComboBox ItemsSource="{Binding AvailableModels}"
                                  SelectedItem="{Binding SelectedModel}"
                                  HorizontalAlignment="Stretch"
                                  MinHeight="32">
                            <ComboBox.ItemTemplate>
                                <DataTemplate DataType="models:TDA4Model">
                                    <TextBlock Text="{Binding Converter={StaticResource TDA4ModelToDisplayNameConverter}}"/>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <!-- Model Description -->
                        <TextBlock Text="{Binding SelectedModelDescription}"
                                   TextWrapping="Wrap"
                                   FontSize="12"
                                   Foreground="#666666"
                                   Margin="0,8,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Model List Items -->
                <StackPanel Spacing="4" Margin="16,0,0,0">
                    <TextBlock Text="• TDA4VM" FontSize="12"/>
                    <TextBlock Text="• Dummy model 1" FontSize="12"/>
                    <TextBlock Text="• Dummy model 2" FontSize="12"/>
                </StackPanel>
            </StackPanel>

            <!-- Test Mode Selection -->
            <StackPanel Grid.Column="2" Spacing="16">
                <Label Content="Test mode selection (Dropdown list)" 
                       Classes="section" FontSize="16"/>
                
                <Border BorderBrush="#CCCCCC" BorderThickness="1" 
                        CornerRadius="4" Padding="16" Background="White">
                    <StackPanel Spacing="12">
                        <ComboBox ItemsSource="{Binding AvailableTestModes}"
                                  SelectedItem="{Binding SelectedTestMode}"
                                  HorizontalAlignment="Stretch"
                                  MinHeight="32">
                            <ComboBox.ItemTemplate>
                                <DataTemplate DataType="models:TestMode">
                                    <TextBlock Text="{Binding Converter={StaticResource TestModeToDisplayNameConverter}}"/>
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>

                        <!-- Test Mode Description -->
                        <TextBlock Text="{Binding SelectedTestModeDescription}"
                                   TextWrapping="Wrap"
                                   FontSize="12"
                                   Foreground="#666666"
                                   Margin="0,8,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Test Mode List Items -->
                <StackPanel Spacing="4" Margin="16,0,0,0">
                    <TextBlock Text="• Inter-processor communication" FontSize="12"/>
                    <TextBlock Text="• Dummy test mode 1" FontSize="12"/>
                    <TextBlock Text="• Dummy test mode 2" FontSize="12"/>
                </StackPanel>
            </StackPanel>
        </Grid>

        <!-- Navigation -->
        <Grid Grid.Row="2" Margin="0,24,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Validation Status -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" 
                        VerticalAlignment="Center" Spacing="8">
                <Ellipse Width="12" Height="12" 
                         Fill="{Binding CanNavigateNext, Converter={StaticResource BoolToColorConverter}}"/>
                <TextBlock Text="{Binding CanNavigateNext, StringFormat='Step Valid: {0}'}"
                           VerticalAlignment="Center" FontSize="12"/>
            </StackPanel>

            <!-- Next Button -->
            <Button Grid.Column="1" Content="Next" Classes="primary"
                    Command="{Binding NextCommand}"
                    IsEnabled="{Binding CanNavigateNext}"
                    MinWidth="100" Padding="16,8"/>
        </Grid>
    </Grid>

</UserControl>
