<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="using:TDA4_Emulator.ViewModels"
        xmlns:views="using:TDA4_Emulator.Views"
        xmlns:models="using:TDA4_Emulator.Models"
        xmlns:converters="using:TDA4_Emulator.Converters"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d" d:DesignWidth="1600" d:DesignHeight="900"
        x:Class="TDA4_Emulator.Views.MainWindow"
        x:DataType="vm:MainWindowViewModel"
        Icon="/Assets/logo_icon.png"
        Title="TDA4 Emulator Control Panel"
        Width="1600" Height="900"
        MinWidth="1024" MinHeight="768"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <converters:CoreTypeToDisplayNameConverter x:Key="CoreTypeToDisplayNameConverter"/>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:TestModeToViewConverter x:Key="TestModeToViewConverter"/>
        <converters:CollectionContainsConverter x:Key="CollectionContainsConverter"/>
    </Window.Resources>

    <Design.DataContext>
        <vm:MainWindowViewModel/>
    </Design.DataContext>

    <Window.Styles>
        <!-- Define custom styles for the bordeaux/gray theme -->
        <Style Selector="Button.primary">
            <Setter Property="Background" Value="#800020"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#600015"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <Style Selector="Button.primary:pointerover">
            <Setter Property="Background" Value="#A00025"/>
        </Style>

        <Style Selector="Button.secondary">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="4"/>
        </Style>

        <Style Selector="Button.secondary:pointerover">
            <Setter Property="Background" Value="#E8E8E8"/>
        </Style>

        <Style Selector="TextBox.terminal">
            <Setter Property="Background" Value="#FFFFFF"/>
            <Setter Property="Foreground" Value="#000000"/>
            <Setter Property="FontFamily" Value="Consolas, Monaco, monospace"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
            <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
            <Setter Property="CaretBrush" Value="Transparent"/>
            <Setter Property="SelectionBrush" Value="#E0E0E0"/>
        </Style>

        <Style Selector="TextBox.terminal:focus">
            <Setter Property="Background" Value="#FFFFFF"/>
            <Setter Property="BorderBrush" Value="#800020"/>
        </Style>

        <Style Selector="TextBox.path">
            <Setter Property="Margin" Value="4"/>
            <Setter Property="Padding" Value="8,4"/>
        </Style>

        <Style Selector="TextBox.error">
            <Setter Property="BorderBrush" Value="Red"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>

        <Style Selector="Label.section">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#800020"/>
            <Setter Property="Margin" Value="0,8,0,4"/>
        </Style>

        <!-- Custom checkbox styles with bordeaux highlight and no borders -->
        <Style Selector="CheckBox">
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="CheckBox /template/ Border#NormalRectangle">
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Background" Value="Transparent"/>
        </Style>

        <Style Selector="CheckBox:checked /template/ Border#NormalRectangle">
            <Setter Property="Background" Value="#800020"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="CheckBox:checked:pointerover /template/ Border#NormalRectangle">
            <Setter Property="Background" Value="#A00025"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <Style Selector="CheckBox:pointerover /template/ Border#NormalRectangle">
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>
    </Window.Styles>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Header -->
            <RowDefinition Height="*"/>    <!-- Main Content -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Background="#800020" Padding="12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Logo -->
                <Border Grid.Column="0" Background="White" Width="150" Height="50"
                        CornerRadius="4" Margin="0,0,12,0">
                    <Image Source="/Assets/logo_text.png"
                           Stretch="Uniform"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Margin="4"/>
                </Border>

                <!-- Title -->
                <TextBlock Grid.Column="1" Text="TDA4 Emulator Control Panel"
                           FontSize="24" FontWeight="Bold" Foreground="White"
                           VerticalAlignment="Center"/>

                <!-- Help Button -->
                <Button Grid.Column="2" Content="Help" Classes="secondary"
                        Command="{Binding ShowHelpCommand}"/>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="12">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>  <!-- Step Navigation Panel -->
                <ColumnDefinition Width="12"/>   <!-- Splitter -->
                <ColumnDefinition Width="*"/>    <!-- Main Content Area -->
            </Grid.ColumnDefinitions>

            <!-- Step Navigation Panel (Left Side) -->
            <Border Grid.Column="0" Background="#F8F8F8" BorderBrush="#CCCCCC"
                    BorderThickness="1" CornerRadius="4" Padding="16">
                <StackPanel Spacing="24">

                    <!-- Step 1 - Model Selection -->
                    <StackPanel Spacing="8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Step Circle -->
                            <Border Grid.Column="0" Width="32" Height="32"
                                    CornerRadius="16" Margin="0,0,12,0"
                                    Background="{Binding IsStep1, Converter={StaticResource BoolToColorConverter}}"
                                    BorderBrush="#800020" BorderThickness="2">
                                <TextBlock Text="1" HorizontalAlignment="Center"
                                           VerticalAlignment="Center" FontWeight="Bold"
                                           Foreground="White"/>
                            </Border>

                            <!-- Step Text -->
                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                <TextBlock Text="Model Selection" FontWeight="Bold"
                                           Foreground="{Binding IsStep1, Converter={StaticResource BoolToColorConverter}}"/>
                                <TextBlock Text="Select TDA4 model and test mode"
                                           FontSize="11" Foreground="#666666" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Grid>

                        <!-- Connection Line -->
                        <Line X1="16" Y1="0" X2="16" Y2="20"
                              Stroke="#CCCCCC" StrokeThickness="2"
                              HorizontalAlignment="Left"/>
                    </StackPanel>

                    <!-- Step 2 - Configuration -->
                    <StackPanel Spacing="8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Step Circle -->
                            <Border Grid.Column="0" Width="32" Height="32"
                                    CornerRadius="16" Margin="0,0,12,0"
                                    Background="{Binding IsStep2, Converter={StaticResource BoolToColorConverter}}"
                                    BorderBrush="#800020" BorderThickness="2">
                                <TextBlock Text="2" HorizontalAlignment="Center"
                                           VerticalAlignment="Center" FontWeight="Bold"
                                           Foreground="White"/>
                            </Border>

                            <!-- Step Text -->
                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                <TextBlock Text="Configuration" FontWeight="Bold"
                                           Foreground="{Binding IsStep2, Converter={StaticResource BoolToColorConverter}}"/>
                                <TextBlock Text="Configure core binary paths"
                                           FontSize="11" Foreground="#666666" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Grid>

                        <!-- Connection Line -->
                        <Line X1="16" Y1="0" X2="16" Y2="20"
                              Stroke="#CCCCCC" StrokeThickness="2"
                              HorizontalAlignment="Left"/>
                    </StackPanel>

                    <!-- Step 3 - Emulation -->
                    <StackPanel Spacing="8">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Step Circle -->
                            <Border Grid.Column="0" Width="32" Height="32"
                                    CornerRadius="16" Margin="0,0,12,0"
                                    Background="{Binding IsStep3, Converter={StaticResource BoolToColorConverter}}"
                                    BorderBrush="#800020" BorderThickness="2">
                                <TextBlock Text="3" HorizontalAlignment="Center"
                                           VerticalAlignment="Center" FontWeight="Bold"
                                           Foreground="White"/>
                            </Border>

                            <!-- Step Text -->
                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                <TextBlock Text="Emulation" FontWeight="Bold"
                                           Foreground="{Binding IsStep3, Converter={StaticResource BoolToColorConverter}}"/>
                                <TextBlock Text="Control emulation and IPC"
                                           FontSize="11" Foreground="#666666" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Splitter -->
            <GridSplitter Grid.Column="1" Background="#CCCCCC" Width="2"/>

            <!-- Main Content Area (Dynamic based on current step) -->
            <ContentControl Grid.Column="2" Content="{Binding CurrentStepViewModel}">
                <ContentControl.DataTemplates>
                    <!-- Model Selection View -->
                    <DataTemplate DataType="vm:ModelSelectionViewModel">
                        <views:ModelSelectionView/>
                    </DataTemplate>

                    <!-- Configuration View -->
                    <DataTemplate DataType="vm:ConfigurationViewModel">
                        <views:ConfigurationView/>
                    </DataTemplate>

                    <!-- Emulation View -->
                    <DataTemplate DataType="vm:EmulationViewModel">
                        <views:EmulationView/>
                    </DataTemplate>
                </ContentControl.DataTemplates>
            </ContentControl>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#CCCCCC"
                BorderThickness="0,1,0,0" Padding="12,6">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="{Binding StatusMessage}"
                           VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="12">
                    <TextBlock Text="{Binding EmulationViewModel.IsEmulationRunning, StringFormat='Emulation Running: {0}'}"
                               VerticalAlignment="Center"/>
                    <Ellipse Width="12" Height="12"
                             Fill="{Binding EmulationViewModel.IsEmulationRunning, Converter={StaticResource BoolToColorConverter}}"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>

</Window>
