<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="using:TDA4_Emulator.ViewModels"
             x:Class="TDA4_Emulator.Views.EmulationView"
             x:DataType="vm:EmulationViewModel">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Spacing="8" Margin="0,0,0,24">
            <TextBlock Text="Step 3: Emulation (Inter-Processor Communication)" 
                       FontSize="20" FontWeight="Bold" 
                       Foreground="#800020"/>
            <TextBlock Text="Control emulation and inter-processor communication" 
                       FontSize="14" Foreground="#666666"/>
        </StackPanel>

        <!-- Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="24"/>
                <ColumnDefinition Width="2*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel: Core Selection and Message -->
            <StackPanel Grid.Column="0" Spacing="16">
                
                <!-- Inter-processor communication -->
                <Label Content="Inter-processor communication" 
                       Classes="section" FontSize="16"/>

                <!-- Core Selection -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Source Core Selection -->
                    <StackPanel Grid.Column="0" Margin="0,0,8,0">
                        <Label Content="Source core, check box for (14 cores + 1 all option)"/>
                        <Border BorderBrush="#CCCCCC" BorderThickness="1" 
                                CornerRadius="4" Padding="8" Background="White"
                                MaxHeight="200">
                            <ScrollViewer>
                                <ItemsControl ItemsSource="{Binding SourceCores}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <CheckBox Content="{Binding DisplayName}" 
                                                      IsChecked="{Binding IsSelected}"
                                                      Margin="2"/>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </Border>
                    </StackPanel>

                    <!-- Destination Core Selection -->
                    <StackPanel Grid.Column="1" Margin="8,0,0,0">
                        <Label Content="Destination core, check box for (14 cores + 1 all option)"/>
                        <Border BorderBrush="#CCCCCC" BorderThickness="1" 
                                CornerRadius="4" Padding="8" Background="White"
                                MaxHeight="200">
                            <ScrollViewer>
                                <ItemsControl ItemsSource="{Binding DestinationCores}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <CheckBox Content="{Binding DisplayName}" 
                                                      IsChecked="{Binding IsSelected}"
                                                      Margin="2"/>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </Border>
                    </StackPanel>
                </Grid>

                <!-- Message Interface -->
                <StackPanel Spacing="8">
                    <Label Content="Message"/>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBox Grid.Row="0" Text="{Binding Message}"
                                 Watermark="Enter command/message to send between cores..."
                                 Height="80" TextWrapping="Wrap" AcceptsReturn="True"/>
                        
                        <Button Grid.Row="1" Content="Send message" Classes="primary"
                                Command="{Binding SendMessageCommand}"
                                IsEnabled="{Binding CanSendMessage}"
                                HorizontalAlignment="Right"
                                Margin="0,8,0,0"/>
                    </Grid>
                </StackPanel>
            </StackPanel>

            <!-- Right Panel: Communication Channels and Terminal -->
            <StackPanel Grid.Column="2" Spacing="16">
                
                <!-- Communication Channels -->
                <StackPanel Spacing="8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <Label Grid.Column="0" Content="List of pair of cores which based on the selected source and destination core" 
                               Classes="section" FontSize="14"/>
                        
                        <Button Grid.Column="1" Content="Show all" Classes="secondary"
                                Command="{Binding ShowAllCommand}"/>
                    </Grid>
                    
                    <Border BorderBrush="#CCCCCC" BorderThickness="1" 
                            CornerRadius="4" Background="White" MaxHeight="150">
                        <ScrollViewer>
                            <ItemsControl ItemsSource="{Binding CommunicationChannels}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Button Content="{Binding DisplayName}"
                                                Classes="secondary"
                                                HorizontalAlignment="Stretch"
                                                HorizontalContentAlignment="Left"
                                                Margin="4,2"
                                                Command="{Binding $parent[UserControl].DataContext.SelectChannelCommand}"
                                                CommandParameter="{Binding}"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Border>
                </StackPanel>

                <!-- Terminal Output -->
                <StackPanel Spacing="8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <Label Grid.Column="0" Content="Terminal Output" 
                               Classes="section" FontSize="14"/>
                        
                        <Button Grid.Column="1" Content="Clear" Classes="secondary"
                                Command="{Binding ClearTerminalCommand}"/>
                    </Grid>
                    
                    <Border BorderBrush="#CCCCCC" BorderThickness="1" 
                            CornerRadius="4" Background="White">
                        <TextBox Classes="terminal"
                                 Text="{Binding TerminalOutputText}"
                                 MinHeight="200"
                                 IsReadOnly="True"/>
                    </Border>
                </StackPanel>
            </StackPanel>
        </Grid>

        <!-- Navigation -->
        <Grid Grid.Row="2" Margin="0,24,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Back Button -->
            <Button Grid.Column="0" Content="Back" Classes="secondary"
                    Command="{Binding BackCommand}"
                    MinWidth="100" Padding="16,8"/>

            <!-- Emulation Status -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" 
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center" Spacing="8">
                <Ellipse Width="12" Height="12" 
                         Fill="{Binding IsEmulationRunning, Converter={StaticResource BoolToColorConverter}}"/>
                <TextBlock Text="{Binding IsEmulationRunning, StringFormat='Emulation Running: {0}'}"
                           VerticalAlignment="Center" FontSize="12"/>
            </StackPanel>

            <!-- Stop Emulation Button -->
            <Button Grid.Column="2" Content="Stop Emulation" Classes="primary"
                    Command="{Binding StopEmulationCommand}"
                    IsEnabled="{Binding IsEmulationRunning}"
                    MinWidth="120" Padding="16,8"
                    Margin="0,0,8,0"/>

            <!-- Start Emulation Button -->
            <Button Grid.Column="3" Content="Start Emulation" Classes="primary"
                    Command="{Binding StartEmulationCommand}"
                    IsEnabled="{Binding CanStartEmulation}"
                    MinWidth="120" Padding="16,8"/>
        </Grid>
    </Grid>

</UserControl>
