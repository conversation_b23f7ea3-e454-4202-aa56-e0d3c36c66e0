using Avalonia.Controls;
using System.ComponentModel;
using System.Threading.Tasks;
using TDA4_Emulator.ViewModels;

namespace TDA4_Emulator.Views;

public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();

        // Handle window closing event to ensure proper cleanup
        Closing += OnWindowClosing;
    }

    private async void OnWindowClosing(object? sender, WindowClosingEventArgs e)
    {
        // If we're already in the process of closing, don't interfere
        if (e.<PERSON><PERSON>)
            return;

        // Get the view model
        if (DataContext is not MainWindowViewModel viewModel)
            return;

        // Check if there are any running processes that need to be stopped
        if (viewModel.IsEmulationRunning)
        {
            // Cancel the close operation temporarily
            e.Cancel = true;

            // Perform graceful shutdown in the background
            await PerformGracefulShutdown(viewModel);

            // After cleanup is complete, close the window
            // Use BeginInvoke to avoid potential deadlocks
            _ = Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                // Temporarily remove the event handler to avoid recursion
                Closing -= OnWindowClosing;
                Close();
            });
        }
    }

    private async Task PerformGracefulShutdown(MainWindowViewModel viewModel)
    {
        try
        {
            // Update status to indicate shutdown is in progress
            viewModel.StatusMessage = "Shutting down application and stopping processes...";

            // Stop all running processes gracefully
            await viewModel.ShutdownAsync();
        }
        catch (System.Exception ex)
        {
            // Log the error but continue with shutdown
            viewModel.StatusMessage = $"Error during shutdown: {ex.Message}";

            // Still try to dispose resources
            viewModel.Dispose();
        }
    }
}