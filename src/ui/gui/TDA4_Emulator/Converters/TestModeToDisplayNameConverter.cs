using System;
using System.Globalization;
using Avalonia.Data.Converters;
using TDA4_Emulator.Models;

namespace TDA4_Emulator.Converters;

/// <summary>
/// Converter that converts TestMode enum to display name
/// </summary>
public class TestModeToDisplayNameConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is TestMode testMode)
        {
            return testMode.GetDisplayName();
        }

        return value?.ToString() ?? string.Empty;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
