using System;
using System.Globalization;
using Avalonia.Data.Converters;
using TDA4_Emulator.Models;

namespace TDA4_Emulator.Converters;

/// <summary>
/// Converter that converts TDA4Model enum to display name
/// </summary>
public class TDA4ModelToDisplayNameConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is TDA4Model model)
        {
            return model.GetDisplayName();
        }

        return value?.ToString() ?? string.Empty;
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
