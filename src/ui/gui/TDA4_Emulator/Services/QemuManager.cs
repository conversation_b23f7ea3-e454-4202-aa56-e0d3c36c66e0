using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.Services;

/// <summary>
/// Manages QEMU binary path resolution and validation
/// </summary>
public class QemuManager
{
    private readonly LoggingService _logger;
    private const string QemuBinDirectory = "qemu_bin";

    // Supported QEMU binary names in order of preference
    private static readonly string[] SupportedQemuBinaryNames = {
        "qemu-system-aarch64.exe",
        "qemu-system-aarch64",
        "qemu-system-arm.exe",
        "qemu-system-arm"
    };

    public QemuManager(LoggingService logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Gets the path to the QEMU binary, preferring custom path over default
    /// </summary>
    /// <param name="customQemuPath">Custom QEMU binary path (optional)</param>
    /// <returns>Path to the QEMU binary to use</returns>
    public string GetQemuBinaryPath(string? customQemuPath = null)
    {
        // If custom path is provided and valid, use it
        if (!string.IsNullOrWhiteSpace(customQemuPath) && File.Exists(customQemuPath))
        {
            _logger.LogInfo($"Using custom QEMU binary: {customQemuPath}");
            return customQemuPath;
        }

        // Fall back to default QEMU binary in Assets folder
        var defaultPath = GetDefaultQemuBinaryPath();
        if (File.Exists(defaultPath))
        {
            _logger.LogInfo($"Using default QEMU binary: {defaultPath}");
            return defaultPath;
        }

        throw new FileNotFoundException($"QEMU binary not found. Checked custom path: '{customQemuPath}' and default path: '{defaultPath}'");
    }

    /// <summary>
    /// Gets the path to the default QEMU binary in the Assets/qemu_bin folder
    /// </summary>
    /// <returns>Path to the default QEMU binary</returns>
    public string GetDefaultQemuBinaryPath()
    {
        try
        {
            // Get the application's base directory
            var appDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            if (string.IsNullOrEmpty(appDirectory))
            {
                throw new InvalidOperationException("Could not determine application directory");
            }

            // Construct path to qemu_bin folder
            var qemuBinPath = Path.Combine(appDirectory, "Assets", QemuBinDirectory);

            // Find the first available QEMU binary
            var detectedBinary = DetectQemuBinary(qemuBinPath);
            if (!string.IsNullOrEmpty(detectedBinary))
            {
                return detectedBinary;
            }

            // If no binary found, return the path to the preferred binary name
            var preferredPath = Path.Combine(qemuBinPath, SupportedQemuBinaryNames[0]);
            _logger.LogWarning($"No QEMU binary detected in {qemuBinPath}, returning preferred path: {preferredPath}");
            return preferredPath;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Failed to get default QEMU binary path: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Validates that the QEMU binary exists and is executable
    /// </summary>
    /// <param name="qemuPath">Path to the QEMU binary</param>
    /// <returns>True if the binary is valid, false otherwise</returns>
    public bool ValidateQemuBinary(string qemuPath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(qemuPath))
            {
                _logger.LogWarning("QEMU binary path is null or empty");
                return false;
            }

            if (!File.Exists(qemuPath))
            {
                _logger.LogWarning($"QEMU binary not found: {qemuPath}");
                return false;
            }

            // Additional validation could be added here (e.g., checking file signature)
            _logger.LogInfo($"QEMU binary validation successful: {qemuPath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error validating QEMU binary '{qemuPath}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Checks if the default QEMU binary is available
    /// </summary>
    /// <returns>True if the default QEMU binary exists, false otherwise</returns>
    public bool IsDefaultQemuBinaryAvailable()
    {
        try
        {
            var defaultPath = GetDefaultQemuBinaryPath();
            return File.Exists(defaultPath);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Detects the first available QEMU binary in the specified directory
    /// </summary>
    /// <param name="qemuBinPath">Path to the qemu_bin directory</param>
    /// <returns>Full path to the detected QEMU binary, or null if none found</returns>
    private string? DetectQemuBinary(string qemuBinPath)
    {
        try
        {
            if (!Directory.Exists(qemuBinPath))
            {
                _logger.LogWarning($"QEMU binary directory does not exist: {qemuBinPath}");
                return null;
            }

            // First, try to find binaries by preferred names
            foreach (var binaryName in SupportedQemuBinaryNames)
            {
                var binaryPath = Path.Combine(qemuBinPath, binaryName);
                if (File.Exists(binaryPath))
                {
                    _logger.LogInfo($"Detected QEMU binary by name: {binaryPath}");
                    return binaryPath;
                }
            }

            // If no preferred names found, scan for any executable files
            var detectedBinary = ScanForExecutableFiles(qemuBinPath);
            if (!string.IsNullOrEmpty(detectedBinary))
            {
                _logger.LogInfo($"Detected QEMU binary by scan: {detectedBinary}");
                return detectedBinary;
            }

            _logger.LogWarning($"No QEMU binary detected in directory: {qemuBinPath}");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error detecting QEMU binary in {qemuBinPath}: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Scans a directory for executable files that might be QEMU binaries
    /// </summary>
    /// <param name="directory">Directory to scan</param>
    /// <returns>Path to the first executable file found, or null</returns>
    private string? ScanForExecutableFiles(string directory)
    {
        try
        {
            var files = Directory.GetFiles(directory);

            // Look for files with executable extensions first
            var executableExtensions = new[] { ".exe", ".bin", "" }; // Empty string for files without extension

            foreach (var extension in executableExtensions)
            {
                var executableFiles = files.Where(f =>
                    Path.GetExtension(f).Equals(extension, StringComparison.OrdinalIgnoreCase) &&
                    IsLikelyQemuBinary(Path.GetFileName(f))
                ).ToArray();

                if (executableFiles.Any())
                {
                    return executableFiles.First();
                }
            }

            // If no obvious executables found, return the first file that looks like QEMU
            var qemuLikeFiles = files.Where(f => IsLikelyQemuBinary(Path.GetFileName(f))).ToArray();
            return qemuLikeFiles.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error scanning for executable files in {directory}: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Determines if a filename is likely to be a QEMU binary
    /// </summary>
    /// <param name="filename">The filename to check</param>
    /// <returns>True if the filename suggests it's a QEMU binary</returns>
    private static bool IsLikelyQemuBinary(string filename)
    {
        if (string.IsNullOrEmpty(filename))
            return false;

        var lowerFilename = filename.ToLowerInvariant();
        var qemuKeywords = new[] { "qemu", "tda4", "emulator", "emu" };

        return qemuKeywords.Any(keyword => lowerFilename.Contains(keyword));
    }

    /// <summary>
    /// Gets all available QEMU binaries in the qemu_bin directory
    /// </summary>
    /// <returns>List of available QEMU binary paths</returns>
    public List<string> GetAvailableQemuBinaries()
    {
        var availableBinaries = new List<string>();

        try
        {
            var appDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            if (string.IsNullOrEmpty(appDirectory))
                return availableBinaries;

            var qemuBinPath = Path.Combine(appDirectory, "Assets", QemuBinDirectory);

            if (!Directory.Exists(qemuBinPath))
                return availableBinaries;

            // Add all supported binaries that exist
            foreach (var binaryName in SupportedQemuBinaryNames)
            {
                var binaryPath = Path.Combine(qemuBinPath, binaryName);
                if (File.Exists(binaryPath))
                {
                    availableBinaries.Add(binaryPath);
                }
            }

            // Add any other executable files found
            var files = Directory.GetFiles(qemuBinPath);
            foreach (var file in files)
            {
                if (!availableBinaries.Contains(file) && IsLikelyQemuBinary(Path.GetFileName(file)))
                {
                    availableBinaries.Add(file);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error getting available QEMU binaries: {ex.Message}");
        }

        return availableBinaries;
    }
}
