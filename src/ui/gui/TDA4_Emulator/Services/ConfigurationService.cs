using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using TDA4_Emulator.Models;

namespace TDA4_Emulator.Services;

/// <summary>
/// Service for managing application configuration
/// </summary>
public class ConfigurationService
{
    private readonly LoggingService _logger;
    private readonly string _configFilePath;
    private ApplicationConfiguration? _currentConfiguration;

    /// <summary>
    /// Event raised when configuration is loaded or changed
    /// </summary>
    public event EventHandler<ApplicationConfiguration>? ConfigurationChanged;

    public ConfigurationService(LoggingService logger)
    {
        _logger = logger;
        _configFilePath = Path.Combine(
            Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) ?? ".",
            "Assets",
            "config.json"
        );
    }

    /// <summary>
    /// Gets the current configuration, loading it if necessary
    /// </summary>
    public async Task<ApplicationConfiguration> GetConfigurationAsync()
    {
        if (_currentConfiguration == null)
        {
            _currentConfiguration = await LoadConfigurationAsync();
        }
        return _currentConfiguration;
    }

    /// <summary>
    /// Loads configuration from file
    /// </summary>
    public async Task<ApplicationConfiguration> LoadConfigurationAsync()
    {
        try
        {
            _logger.LogInfo($"Loading configuration from: {_configFilePath}");

            if (!File.Exists(_configFilePath))
            {
                _logger.LogInfo("Configuration file not found, creating default configuration");
                var defaultConfig = CreateDefaultConfiguration();
                await SaveConfigurationAsync(defaultConfig);
                return defaultConfig;
            }

            var jsonContent = await File.ReadAllTextAsync(_configFilePath);
            
            if (string.IsNullOrWhiteSpace(jsonContent))
            {
                _logger.LogWarning("Configuration file is empty, using default configuration");
                return CreateDefaultConfiguration();
            }

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                WriteIndented = true,
                AllowTrailingCommas = true
            };

            var configuration = JsonSerializer.Deserialize<ApplicationConfiguration>(jsonContent, options);

            if (configuration == null)
            {
                _logger.LogWarning("Failed to deserialize configuration, using default configuration");
                return CreateDefaultConfiguration();
            }

            _currentConfiguration = configuration;
            _logger.LogInfo(configuration.toString());
            _logger.LogInfo("Configuration loaded successfully");
            
            // Validate binary paths if enabled
            if (configuration.Settings.ValidateBinaryPathsOnStartup)
            {
                ValidateBinaryPaths(configuration);
            }

            ConfigurationChanged?.Invoke(this, configuration);
            return configuration;
        }
        catch (JsonException ex)
        {
            _logger.LogError("Invalid JSON in configuration file", ex);
            var defaultConfig = CreateDefaultConfiguration();
            BackupCorruptedConfig();
            await SaveConfigurationAsync(defaultConfig);
            return defaultConfig;
        }
        catch (Exception ex)
        {
            _logger.LogError("Error loading configuration", ex);
            return CreateDefaultConfiguration();
        }
    }

    /// <summary>
    /// Saves configuration to file
    /// </summary>
    public async Task<bool> SaveConfigurationAsync(ApplicationConfiguration configuration)
    {
        try
        {
            _logger.LogInfo($"Saving configuration to: {_configFilePath}");

            // Ensure directory exists
            var directory = Path.GetDirectoryName(_configFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                _logger.LogInfo($"Created configuration directory: {directory}");
            }

            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var jsonContent = JsonSerializer.Serialize(configuration, options);
            await File.WriteAllTextAsync(_configFilePath, jsonContent);

            _currentConfiguration = configuration;
            _logger.LogInfo("Configuration saved successfully");
            
            ConfigurationChanged?.Invoke(this, configuration);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError("Error saving configuration", ex);
            return false;
        }
    }

    /// <summary>
    /// Updates binary path for a specific core type and optionally saves
    /// </summary>
    public async Task<bool> UpdateBinaryPathAsync(CoreType coreType, string path, bool autoSave = true)
    {
        try
        {
            var config = await GetConfigurationAsync();
            
            switch (coreType)
            {
                case CoreType.R5F:
                    config.R5FBinaryPaths = path ?? string.Empty;
                    break;
                case CoreType.A72:
                    config.A72BinaryPaths = path ?? string.Empty;
                    break;
                case CoreType.C7x:
                    config.C7xBinaryPaths = path ?? string.Empty;
                    break;
                default:
                    _logger.LogWarning($"Cannot update binary path for core type: {coreType}");
                    return false;
            }

            if (autoSave && config.Settings.AutoSaveConfiguration)
            {
                return await SaveConfigurationAsync(config);
            }

            _currentConfiguration = config;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error updating binary path for {coreType}", ex);
            return false;
        }
    }

    /// <summary>
    /// Updates QEMU binary path and optionally saves
    /// </summary>
    public async Task<bool> UpdateQemuBinaryPathAsync(string path, bool autoSave = true)
    {
        try
        {
            var config = await GetConfigurationAsync();
            config.QemuBinaryPaths.SetPath(path ?? string.Empty);

            if (autoSave && config.Settings.AutoSaveConfiguration)
            {
                return await SaveConfigurationAsync(config);
            }

            _currentConfiguration = config;
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError("Error updating QEMU binary path", ex);
            return false;
        }
    }

    /// <summary>
    /// Validates all configured binary paths
    /// </summary>
    public Dictionary<CoreType, ValidationResult> ValidateBinaryPaths(ApplicationConfiguration configuration)
    {
        var results = new Dictionary<CoreType, ValidationResult>();

        // Validate core binary paths
        results[CoreType.R5F] = ValidateBinaryPath(configuration.QemuBinaryPaths.GetPath(), "QEMU binary");
        results[CoreType.R5F] = ValidateBinaryPath(configuration.R5FBinaryPaths, "R5F binary");
        results[CoreType.A72] = ValidateBinaryPath(configuration.A72BinaryPaths, "A72 binary");
        results[CoreType.C7x] = ValidateBinaryPath(configuration.C7xBinaryPaths, "C7x binary");

        // Log validation results
        foreach (var result in results)
        {
            if (!result.Value.IsValid)
            {
                _logger.LogWarning($"{result.Key} binary path validation failed: {result.Value.ErrorMessage}");
            }
        }

        return results;
    }

    /// <summary>
    /// Resets configuration to default values
    /// </summary>
    public async Task<bool> ResetToDefaultAsync()
    {
        try
        {
            _logger.LogInfo("Resetting configuration to default values");
            var defaultConfig = CreateDefaultConfiguration();
            return await SaveConfigurationAsync(defaultConfig);
        }
        catch (Exception ex)
        {
            _logger.LogError("Error resetting configuration to default", ex);
            return false;
        }
    }

    /// <summary>
    /// Creates default configuration
    /// </summary>
    private ApplicationConfiguration CreateDefaultConfiguration()
    {
        return new ApplicationConfiguration
        {
            QemuBinaryPaths = new QemuBinaryPaths(),
            R5FBinaryPaths = string.Empty,
            A72BinaryPaths = string.Empty,
            C7xBinaryPaths = string.Empty,
            Settings = new ApplicationSettings
            {
                AutoSaveConfiguration = true,
                ValidateBinaryPathsOnStartup = true,
                ProcessTimeoutSeconds = 30
            }
        };
    }

    /// <summary>
    /// Validates a single binary path
    /// </summary>
    private ValidationResult ValidateBinaryPath(string path, string description)
    {
        if (string.IsNullOrWhiteSpace(path))
        {
            return new ValidationResult { IsValid = true, ErrorMessage = string.Empty }; // Empty paths are valid (optional)
        }

        if (!File.Exists(path))
        {
            return new ValidationResult { IsValid = false, ErrorMessage = $"{description} file not found: {path}" };
        }

        if (!IsExecutableFile(path))
        {
            return new ValidationResult { IsValid = false, ErrorMessage = $"{description} is not an executable file: {path}" };
        }

        return new ValidationResult { IsValid = true, ErrorMessage = string.Empty };
    }

    /// <summary>
    /// Checks if a file is executable
    /// </summary>
    private bool IsExecutableFile(string filePath)
    {
        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".exe" || extension == ".bat" || extension == ".cmd";
        }

        // On Unix-like systems, this would require more sophisticated permission checking
        return true;
    }

    /// <summary>
    /// Backs up corrupted configuration file
    /// </summary>
    private void BackupCorruptedConfig()
    {
        try
        {
            if (File.Exists(_configFilePath))
            {
                var backupPath = $"{_configFilePath}.backup.{DateTime.Now:yyyyMMdd_HHmmss}";
                File.Copy(_configFilePath, backupPath);
                _logger.LogInfo($"Corrupted configuration backed up to: {backupPath}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("Error backing up corrupted configuration", ex);
        }
    }
}

/// <summary>
/// Result of binary path validation
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}
