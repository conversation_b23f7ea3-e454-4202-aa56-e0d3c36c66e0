using System;
using System.Collections.Generic;
using System.Linq;
using TDA4_Emulator.Models;

namespace TDA4_Emulator.Services;

/// <summary>
/// Service for managing wizard navigation and step validation
/// </summary>
public class WizardNavigationService
{
    private readonly LoggingService _logger;
    private WizardStep _currentStep = WizardStep.ModelSelection;
    private readonly Dictionary<WizardStep, bool> _stepValidation = new();

    /// <summary>
    /// Event raised when the current step changes
    /// </summary>
    public event EventHandler<WizardStepChangedEventArgs>? CurrentStepChanged;

    /// <summary>
    /// Event raised when step validation status changes
    /// </summary>
    public event EventHandler<StepValidationChangedEventArgs>? StepValidationChanged;

    public WizardNavigationService(LoggingService logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Initialize step validation states
        foreach (var step in WizardStepExtensions.GetAllSteps())
        {
            _stepValidation[step] = false;
        }
        
        _logger.LogInfo("WizardNavigationService initialized");
    }

    /// <summary>
    /// Gets the current wizard step
    /// </summary>
    public WizardStep CurrentStep
    {
        get => _currentStep;
        private set
        {
            if (_currentStep != value)
            {
                var oldStep = _currentStep;
                _currentStep = value;
                _logger.LogInfo($"Wizard step changed from {oldStep} to {value}");
                CurrentStepChanged?.Invoke(this, new WizardStepChangedEventArgs(oldStep, value));
            }
        }
    }

    /// <summary>
    /// Determines if the current step can navigate to the next step
    /// </summary>
    public bool CanNavigateNext => CurrentStep.CanNavigateNext() && IsStepValid(CurrentStep);

    /// <summary>
    /// Determines if the current step can navigate to the previous step
    /// </summary>
    public bool CanNavigatePrevious => CurrentStep.CanNavigatePrevious();

    /// <summary>
    /// Navigates to the next step if possible
    /// </summary>
    public bool NavigateNext()
    {
        if (!CanNavigateNext)
        {
            _logger.LogWarning($"Cannot navigate next from step {CurrentStep}. Valid={IsStepValid(CurrentStep)}, CanNavigate={CurrentStep.CanNavigateNext()}");
            return false;
        }

        var nextStep = CurrentStep.GetNextStep();
        if (nextStep.HasValue)
        {
            CurrentStep = nextStep.Value;
            return true;
        }

        return false;
    }

    /// <summary>
    /// Navigates to the previous step if possible
    /// </summary>
    public bool NavigatePrevious()
    {
        if (!CanNavigatePrevious)
        {
            _logger.LogWarning($"Cannot navigate previous from step {CurrentStep}");
            return false;
        }

        var previousStep = CurrentStep.GetPreviousStep();
        if (previousStep.HasValue)
        {
            CurrentStep = previousStep.Value;
            return true;
        }

        return false;
    }

    /// <summary>
    /// Navigates directly to a specific step
    /// </summary>
    public bool NavigateToStep(WizardStep targetStep)
    {
        // Validate that we can reach the target step
        if (!CanNavigateToStep(targetStep))
        {
            _logger.LogWarning($"Cannot navigate to step {targetStep} from {CurrentStep}");
            return false;
        }

        CurrentStep = targetStep;
        return true;
    }

    /// <summary>
    /// Determines if we can navigate to a specific step
    /// </summary>
    public bool CanNavigateToStep(WizardStep targetStep)
    {
        // Can always go back to previous steps
        if (targetStep.GetStepNumber() < CurrentStep.GetStepNumber())
        {
            return true;
        }

        // Can only go forward if all intermediate steps are valid
        var currentStepNumber = CurrentStep.GetStepNumber();
        var targetStepNumber = targetStep.GetStepNumber();

        for (int stepNumber = currentStepNumber; stepNumber < targetStepNumber; stepNumber++)
        {
            var step = GetStepByNumber(stepNumber);
            if (step.HasValue && !IsStepValid(step.Value))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// Sets the validation status for a specific step
    /// </summary>
    public void SetStepValidation(WizardStep step, bool isValid)
    {
        if (_stepValidation.TryGetValue(step, out var currentValid) && currentValid != isValid)
        {
            _stepValidation[step] = isValid;
            _logger.LogInfo($"Step {step} validation changed to {isValid}");
            StepValidationChanged?.Invoke(this, new StepValidationChangedEventArgs(step, isValid));
        }
        else if (!_stepValidation.ContainsKey(step))
        {
            _stepValidation[step] = isValid;
            _logger.LogInfo($"Step {step} validation set to {isValid}");
            StepValidationChanged?.Invoke(this, new StepValidationChangedEventArgs(step, isValid));
        }
    }

    /// <summary>
    /// Gets the validation status for a specific step
    /// </summary>
    public bool IsStepValid(WizardStep step)
    {
        return _stepValidation.TryGetValue(step, out var isValid) && isValid;
    }

    /// <summary>
    /// Gets all step validation statuses
    /// </summary>
    public Dictionary<WizardStep, bool> GetAllStepValidations()
    {
        return new Dictionary<WizardStep, bool>(_stepValidation);
    }

    /// <summary>
    /// Resets the wizard to the first step
    /// </summary>
    public void Reset()
    {
        _logger.LogInfo("Resetting wizard navigation");
        CurrentStep = WizardStep.ModelSelection;
        
        // Reset all validations
        foreach (var step in WizardStepExtensions.GetAllSteps())
        {
            SetStepValidation(step, false);
        }
    }

    /// <summary>
    /// Gets a wizard step by its number (1-based)
    /// </summary>
    private WizardStep? GetStepByNumber(int stepNumber)
    {
        return WizardStepExtensions.GetAllSteps().FirstOrDefault(s => s.GetStepNumber() == stepNumber);
    }
}

/// <summary>
/// Event arguments for wizard step changes
/// </summary>
public class WizardStepChangedEventArgs : EventArgs
{
    public WizardStep OldStep { get; }
    public WizardStep NewStep { get; }

    public WizardStepChangedEventArgs(WizardStep oldStep, WizardStep newStep)
    {
        OldStep = oldStep;
        NewStep = newStep;
    }
}

/// <summary>
/// Event arguments for step validation changes
/// </summary>
public class StepValidationChangedEventArgs : EventArgs
{
    public WizardStep Step { get; }
    public bool IsValid { get; }

    public StepValidationChangedEventArgs(WizardStep step, bool isValid)
    {
        Step = step;
        IsValid = isValid;
    }
}
