**TDA4 Emulator Control Panel - Application Design Specification**

A comprehensive cross-platform desktop application built with **Avalonia UI 11.x** and **ReactiveUI** using **C# .NET 9.0** and **MVVM pattern** to control TDA4 Emulator processes. The application features a **wizard-style three-step workflow** for model selection, configuration, and emulation control.

## **1. UI Architecture & Layout**

### **Header Section**:
- **Application Title**: "TDA4 Emulator Control Panel" with professional styling
- **Help Button**: Opens comprehensive help dialog with detailed documentation positioned in top-right corner
- **Application Icon**: Custom PNG icon for window and taskbar

### **Main Layout** (Wizard-Style Three-Step Process):
- **Left Panel** (200px): Vertical step navigation with highlighted current step
- **Main Content Area** (Expandable): Dynamic content based on current step
- **Navigation Controls**: Next/Back buttons for step progression
- **Status Bar** (Bottom): Current operation status and progress indicators

### **Step Navigation Panel** (Left Side):
- **Step 1 - Model Selection**: Highlighted circle with "1" when active
- **Step 2 - Configuration**: Highlighted circle with "2" when active
- **Step 3 - Emulation**: Highlighted circle with "3" when active
- **Visual Indicators**: Connected lines between steps showing workflow progression
- **Current Step Highlighting**: Active step shown with filled circle and bold text

### **Theme & Styling**:
- **Color Scheme**: Bordeaux red (#800020) and light gray (#F0F0F0)
- **Typography**: Clear, readable fonts with consistent sizes and weights
- **Button Styles**: Primary action buttons with bordeaux background and white text
- **Input Fields**: Clean, modern text boxes with subtle bordeaux borders
- **Checkboxes**: Custom styling with bordeaux highlight and no borders
- **Labels & Headings**: Clear section titles with bordeaux color
- **Responsive Design**: Optimized for various screen sizes and resolutions
- **Cross-Platform Consistency**: Native look and feel on Windows, macOS, and Linux
- **Terminal Outputs**: Three-column layout with white background and black text

## **2. Step-by-Step Workflow**

### **Step 1: Model Selection**
- **Model List Selection**:
  - Dropdown list with TDA4 model variants:
    - TDA4VM
    - Dummy model 1
    - Dummy model 2
  - Default selection and validation

- **Test Mode Selection**:
  - Dropdown list with test mode options:
    - Inter-processor communication
    - Dummy test mode 1
    - Dummy test mode 2
  - Mode-specific configuration preparation

- **Navigation**: "Next" button to proceed to Configuration step

### **Step 2: Configuration**
- **Binary path panel**:
  - Input Text + Browse Button Section for each core
  - Text input field with file browser integration
  - Browse button for selecting core binary
  - Real-time path validation and error indicators
  - Contain 4 groups which separted by horizontal line
  - **ARM A72** (Group 1):
    - **A72-VM0**
    - **A72-VM1**

  - **ARM MCU R5F** (Group 2):
    - MCU-R5F-0 through MCU-R5F-5

  - **ARM Main R5F** (Group 3):
    - Main-R5F-0 through Main-R5F-5

  - **DSP C7x** (Group 4):
    - C7x-0 through C7x-3

- **Settings Panel**:
  - qemuBinaryPath input text and browse button
  - autoSaveConfiguration checkbox
  - processTimeoutSeconds input field
  - Real-time validation and error indicators
  - Save/Load configuration buttons:
    - "Reset configuration" button to clear all paths and settings
    - "Load configuration" button to load configuration from file

- **Navigation**: "Back" and "Next" buttons
  - "Next" button disabled until at least one valid binary path is provided. When clicked, app navigates to the Emulation step.
  - "Back" button returns to Model Selection step

### **Step 3: Emulation (Inter-Processor Communication)**
- **Core Selection Interface**:
  - **Source Core Selection**: Checkboxes for 14 cores and "all" option
  - **Destination Core Selection**: Checkboxes for 14 cores and "all" option
  - Real-time validation of core pair selection

- **Message Interface**:
  - **Message Input**: Multi-line text area for command/message input
  - **Send Message**: Button to transmit messages between selected cores

- **Core Communication Display**:
  - **List of Communication chanels**: Visual representation of active communication channels based on the selected source and destination core combinations
  - Each communication chanels showing bidirectional communication. Example: "A72-VM0 <-> C7x-0"
  - When communication chanel clicked, show the terminal output of the selected communication chanel.

- **Emulation Control**:
  - "Back" button to return to Configuration step. When clicked, app navigates to the Configuration step.
  - **Stop Emulation** button to halt current emulation session. When clicked, stop all running processes. Then update the status bar.
  - "Start Emulation" button to launch emulation. When clicked, app runs qemu binary with arguments that contain valid core binary path (Except for C7x). C7x binary is excuted directly without QEMU. Then update status bar.

### **Status Bar** (Bottom):
- Current operation status messages
- Real-time feedback for user actions
- Emulation running status with visual indicators

## **3. Technical Architecture**

### **MVVM Pattern with ReactiveUI**:
- **Models**:
  - `CoreType` enum: A72-VM0, A72-VM1, MCU-R5F-0 to MCU-R5F-5, Main-R5F-0 to Main-R5F-5, C7x-0 to C7x-3
  - `TestMode` enum: Inter-processor communication, Dummy test mode 1, Dummy test mode 2
  - `TDA4Model` enum: TDA4VM, Dummy model 1, Dummy model 2
  - `WizardStep` enum: ModelSelection, Configuration, Emulation
  - `TerminalLine`: Timestamped output with color coding
  - `CoreController`: Individual process lifecycle management
  - `ConfigurationSettings`: Serializable configuration data model

- **ViewModels**:
  - `MainWindowViewModel`: Central wizard navigation and state management
  - `ModelSelectionViewModel`: Step 1 - Model and test mode selection logic
  - `ConfigurationViewModel`: Step 2 - Core configuration and settings management
  - `EmulationViewModel`: Step 3 - IPC communication and emulation control
  - Property validation with real-time feedback
  - Thread-safe UI updates via `Dispatcher.UIThread.InvokeAsync`
  - Comprehensive error handling and logging

- **Views**:
  - `MainWindow.axaml`: Primary wizard container interface
  - `ModelSelectionView.axaml`: Step 1 - Model and test mode selection
  - `ConfigurationView.axaml`: Step 2 - Core configuration interface
  - `EmulationView.axaml`: Step 3 - IPC communication and emulation control
  - `HelpDialog.axaml`: Comprehensive user documentation

- **Services**:
  - `ProcessManager`: Multi-core process orchestration
  - `ConfigurationService`: Save/load configuration settings
  - `LoggingService`: Centralized file-based logging
  - `WizardNavigationService`: Step progression and validation
  - Cross-platform file dialog integration

### **Process Management**:
- **Direct Process Communication**: Raw command transmission via stdin
- **Real-time Output Capture**: stdout/stderr redirection with live streaming
- **Multi-Core Coordination**: Simultaneous management of up to 14 different cores
- **Graceful Lifecycle Management**: Proper startup, monitoring, and shutdown
- **Error Recovery**: Process crash detection and restart capabilities

## **4. Communication Protocol**

### **Local Process IO Redirection**:
- **No Network Dependencies**: Direct stdin/stdout/stderr communication
- **Raw Command Transmission**: No intermediate formatting or protocols
- **Real-time Streaming**: Immediate output capture and display
- **Bidirectional Communication**: Command sending and response monitoring

### **Inter-Processor Communication (IPC)**:
- **Core-to-Core Messaging**: Direct communication between selected source and destination cores
- **14-Core Support**: Full support for all TDA4 cores:
  - ARM Cores: A72-VM0, A72-VM1
  - MCU R5F Cores: MCU-R5F-0 through MCU-R5F-5
  - Main R5F Cores: Main-R5F-0 through Main-R5F-5
  - DSP Cores: C7x-0 through C7x-3
- **Message Format**: Direct text transmission to process stdin with core routing
- **Communication Paths**: Visual representation of active core-to-core channels
- **Timestamped Logging**: Core identification and message tracking
- **Terminal Isolation**: Separate terminal outputs for each active core pair

## **5. Advanced Features**

### **Wizard-Style User Experience**:
- **Guided Workflow**: Three-step process from model selection to emulation
- **Step Validation**: Each step validates input before allowing progression
- **Visual Progress Indicators**: Clear step navigation with highlighted current step
- **Flexible Navigation**: Back/Next buttons with state preservation
- **Context-Sensitive Help**: Step-specific guidance and documentation

### **Configuration Management**:
- **Auto-Save Configuration**: Automatic saving of user preferences and settings
- **Configuration Import/Export**: Save and load complete configuration profiles
- **Reset Functionality**: Quick reset to default configuration
- **Process Timeout Settings**: Configurable timeout for core processes
- **Real-time Validation**: Immediate feedback on configuration changes

### **Cross-Platform Compatibility**:
- **.NET 9.0 Runtime**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Platform-Specific Handling**: Executable detection, file paths, fonts
- **Responsive Design**: Minimum 1024x768, optimized for 1600x900 (Full HD)
- **Native Look & Feel**: Platform-appropriate dialogs and styling

### **User Experience Enhancements**:
- **Professional Interface**: Clean, modern wizard-style design
- **Consistent Terminology**: "Emulation" throughout (not "Simulation")
- **Intuitive Workflow**: Logical step-by-step progression
- **Comprehensive Help**: Built-in documentation with troubleshooting guide
- **Keyboard Shortcuts**: F1 (Help), Enter (Next), Escape (Back)

### **Quality & Reliability**:
- **Input Validation**: Real-time validation with descriptive error messages
- **Thread Safety**: All UI updates properly dispatched to main thread
- **Memory Management**: Proper disposal patterns and resource cleanup
- **Error Handling**: Comprehensive exception handling with user feedback
- **Logging System**: Detailed file-based logging for debugging and audit
- **State Management**: Robust wizard state preservation and recovery

## **6. File Structure & Organization**

```
TDA4_Emulator/
├── Models/
│   ├── CoreType.cs              # Extended core type enumeration (14 cores)
│   ├── TestMode.cs              # Test mode enumeration and extensions
│   ├── TDA4Model.cs             # TDA4 model variants enumeration
│   ├── WizardStep.cs            # Wizard step enumeration
│   ├── TerminalLine.cs          # Terminal output data model
│   ├── ConfigurationSettings.cs # Configuration data model
│   └── CoreController.cs        # Individual core process management
├── ViewModels/
│   ├── ViewModelBase.cs         # Base class with IDisposable
│   ├── MainWindowViewModel.cs   # Main wizard container logic
│   ├── ModelSelectionViewModel.cs # Step 1 - Model selection logic
│   ├── ConfigurationViewModel.cs  # Step 2 - Configuration logic
│   └── EmulationViewModel.cs    # Step 3 - Emulation control logic
├── Views/
│   ├── MainWindow.axaml         # Primary wizard container interface
│   ├── ModelSelectionView.axaml # Step 1 - Model and test mode selection
│   ├── ConfigurationView.axaml  # Step 2 - Core configuration interface
│   ├── EmulationView.axaml      # Step 3 - IPC and emulation control
│   └── HelpDialog.axaml         # Comprehensive help documentation
├── Services/
│   ├── ProcessManager.cs        # Multi-core process orchestration
│   ├── ConfigurationService.cs  # Configuration save/load management
│   ├── WizardNavigationService.cs # Step progression and validation
│   └── LoggingService.cs        # Centralized logging service
├── Converters/
│   ├── CoreTypeToDisplayNameConverter.cs
│   ├── BoolToColorConverter.cs
│   ├── TerminalLinesToTextConverter.cs
│   ├── WizardStepToVisibilityConverter.cs
│   └── TestModeToViewConverter.cs
├── Assets/
│   └── logo_icon.png           # Application icon
└── test_binaries/              # Mock executables for testing
    ├── a72_vm0_mock.bat        # A72-VM0 core mock
    ├── a72_vm1_mock.bat        # A72-VM1 core mock
    ├── mcu_r5f_mock.bat        # MCU R5F cores mock
    ├── main_r5f_mock.bat       # Main R5F cores mock
    └── c7x_mock.bat            # C7x DSP cores mock
```

## **6. Deployment & Distribution**

### **Build Configuration**:
- **Self-Contained Deployment**: Includes .NET runtime for target platforms
- **Platform-Specific Packages**: Windows (exe), macOS (app), Linux (AppImage)
- **Asset Embedding**: Logo and icon files included in application bundle
- **Test Binaries**: Mock executables for demonstration and testing

### **System Requirements**:
- **Minimum RAM**: 4GB
- **Disk Space**: 100MB for application + space for core binaries
- **Display**: 1024x768 minimum, 1600x900 recommended
- **Permissions**: Execute permissions for core binary files

## **8. Key Differentiators**

### **Wizard-Style Interface Design**:
- **Guided User Experience**: Three-step workflow from model selection to emulation
- **Progressive Disclosure**: Information revealed step-by-step to reduce complexity
- **Visual Step Navigation**: Clear progress indicators with highlighted current step
- **Flexible Navigation**: Back/Next progression with state preservation
- **Context-Sensitive Interface**: Each step shows only relevant controls and information

### **Comprehensive Core Support**:
- **14-Core Architecture**: Full support for all TDA4 processor cores
- **Heterogeneous Processing**: ARM Cortex-A72, ARM Cortex-R5F, and TI C7x DSP cores
- **Flexible Core Selection**: Individual core selection for precise IPC testing
- **Real-time Communication**: Live inter-processor communication monitoring
- **Scalable Terminal Interface**: Dedicated terminal outputs for active core pairs

### **Advanced Configuration Management**:
- **Auto-Save Functionality**: Automatic preservation of user settings
- **Configuration Profiles**: Save/load complete configuration sets
- **Real-time Validation**: Immediate feedback on configuration changes
- **Reset Capabilities**: Quick return to default settings
- **Process Timeout Control**: Configurable timeout settings for reliability

### **Professional User Experience**:
- **Clean Modern Interface**: Professional wizard-style design
- **Intuitive Workflow**: Logical step-by-step progression
- **Comprehensive Help System**: Built-in documentation and guidance
- **Cross-Platform Consistency**: Native look and feel on all platforms
- **Responsive Design**: Optimized for various screen sizes and resolutions

### **Robust Process Management**:
- **Multi-Core Orchestration**: Simultaneous management of up to 14 cores
- **Graceful Lifecycle Management**: Proper startup, monitoring, and shutdown
- **Real-time Health Monitoring**: Process status tracking and error detection
- **Automatic Error Recovery**: Process crash detection and restart capabilities
- **Comprehensive Logging**: Detailed file-based logging for debugging and audit

### **Extensible Architecture**:
- **Modular Wizard Design**: Easy addition of new steps and functionality
- **Plugin-Ready Core System**: Support for new core types and configurations
- **Scalable Communication Protocol**: Extensible IPC message handling
- **Future-Proof Interface Patterns**: Ready for additional TDA4 model variants

This specification represents the updated TDA4 Emulator Control Panel with a modern wizard-style interface, incorporating comprehensive core support, advanced configuration management, and professional user experience design.

