# TDA4 Emulator Configuration System

## Overview

The TDA4 Emulator now supports dynamic binary path configuration through an external JSON configuration file, replacing the previous hardcoded binary path approach. This enhancement provides flexibility for different development environments and QEMU installations.

## Configuration File

### Location
- **Path**: `src/ui/gui/TDA4_Emulator/Assets/config.json`
- **Runtime Path**: `{ApplicationDirectory}/Assets/config.json`

### Schema
```json
{
  "qemuBinaryPaths": {
    "qemuBinary": "path/to/qemu-system-arm"
  },
  "r5FBinaryPaths": "path/to/r5f-application.elf",
  "a72BinaryPaths": "path/to/a72-application.elf",
  "c7xBinaryPaths": "path/to/c7x-application.elf",
  "settings": {
    "autoSaveConfiguration": true,
    "validateBinaryPathsOnStartup": true,
    "processTimeoutSeconds": 30
  }
}
```

## Features

### 1. **Dynamic Configuration Loading**
- Configuration is loaded automatically on application startup
- Missing configuration files are created with default (empty) values
- Corrupted configuration files are backed up and replaced with defaults

### 2. **Automatic Configuration Persistence**
- Binary path changes are automatically saved to configuration file
- Auto-save can be disabled via `settings.autoSaveConfiguration`
- Manual save option available through "Save Configuration" button

### 3. **Configuration Management UI**
- **Reset to Default**: Clears all binary paths to empty values
- **Save Configuration**: Manually saves current configuration
- **Browse Buttons**: Select binary files and automatically update configuration

### 4. **Validation System**
- Binary paths are validated on startup (if enabled)
- Real-time validation when paths are changed
- Specific error messages for different failure cases:
  - File not found
  - Not an executable file
  - Wrong file permissions (Unix systems)

### 5. **Error Handling & Recovery**
- Graceful handling of missing configuration files
- Automatic backup of corrupted configuration files
- Fallback to default values on any configuration errors
- Comprehensive logging of all configuration operations

## Usage

### First-Time Setup
1. Launch the application
2. Default configuration file is created automatically
3. Use Browse buttons to select binary files
4. Paths are automatically saved to configuration

### Updating Configuration
1. **Via UI**: Use Browse buttons to select new binary files
2. **Manual Edit**: Edit `Assets/config.json` directly (restart required)
3. **Reset**: Use "Reset to Default" button to clear all paths

### Configuration Validation
- Validation occurs automatically on startup
- Invalid paths are logged with specific error messages
- Application continues to function with invalid paths (user can fix them)

## Technical Implementation

### Classes
- **`ApplicationConfiguration`**: Configuration model with JSON serialization
- **`ConfigurationService`**: Service for loading, saving, and validating configuration
- **`QemuBinaryPaths`**: Nested configuration for QEMU binary paths
- **`ApplicationSettings`**: Application-wide settings

### Integration Points
- **`MainWindowViewModel`**: Loads configuration on startup, auto-saves on changes
- **`ProcessManager`**: Uses configured paths for process execution
- **UI Controls**: Browse buttons and configuration management buttons

### Backward Compatibility
- Application works without configuration file (first-time users)
- Existing browse functionality preserved
- Current validation mechanisms maintained
- No breaking changes to existing workflows

## Configuration Properties

### Binary Paths
- **`qemuBinaryPaths.qemuBinary`**: Path to QEMU system binary used for both R5F and A72 cores
- **`r5FBinaryPaths`**: Path to R5F application binary
- **`a72BinaryPaths`**: Path to A72 application binary
- **`c7xBinaryPaths`**: Path to C7x application binary (runs directly, not through QEMU)

### Settings
- **`autoSaveConfiguration`**: Automatically save configuration on changes (default: true)
- **`validateBinaryPathsOnStartup`**: Validate paths when application starts (default: true)
- **`processTimeoutSeconds`**: Timeout for process operations (default: 30)

## Error Scenarios & Recovery

### Missing Configuration File
- **Behavior**: Create default configuration with empty paths
- **User Action**: Use Browse buttons to configure paths
- **Logging**: Info message about creating default configuration

### Corrupted Configuration File
- **Behavior**: Backup corrupted file, create new default configuration
- **User Action**: Reconfigure paths or restore from backup
- **Logging**: Error message with backup file location

### Invalid Binary Paths
- **Behavior**: Log validation errors, continue operation
- **User Action**: Fix paths via Browse buttons or manual edit
- **Logging**: Warning messages with specific error details

### Configuration Save Failures
- **Behavior**: Log error, continue with in-memory configuration
- **User Action**: Check file permissions, try manual save
- **Logging**: Error message with failure details

## Migration from Previous Version

The new configuration system is fully backward compatible:

1. **No configuration file**: Application creates default configuration
2. **First run**: Use existing Browse buttons to configure paths
3. **Existing workflows**: All current functionality preserved
4. **Enhanced features**: Configuration persistence and validation added

## Troubleshooting

### Configuration Not Loading
- Check file permissions on `Assets/config.json`
- Verify JSON syntax is valid
- Check application logs for detailed error messages

### Paths Not Saving
- Verify `autoSaveConfiguration` is set to `true`
- Check write permissions on Assets directory
- Use "Save Configuration" button for manual save

### Validation Errors
- Ensure binary files exist at specified paths
- Verify files have executable permissions (Unix systems)
- Check file extensions (.exe, .bat, .cmd on Windows)

## Logging

All configuration operations are logged with appropriate levels:
- **Info**: Successful operations, configuration loading/saving
- **Warning**: Validation failures, non-critical errors
- **Error**: Configuration file errors, save failures

Log files are located in: `{ApplicationDirectory}/logs/tda4_emulator_YYYYMMDD.log`
