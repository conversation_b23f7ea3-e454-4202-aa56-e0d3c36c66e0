using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Text;
using System.Threading.Tasks;
using ReactiveUI;
using TDA4_Emulator.Models;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.ViewModels;

/// <summary>
/// ViewModel for Step 3: Emulation
/// </summary>
public class EmulationViewModel : ViewModelBase
{
    private readonly LoggingService _logger;
    private readonly WizardNavigationService _navigationService;
    private readonly ProcessManager _processManager;

    private bool _isEmulationRunning;
    private string _message = string.Empty;
    private string _selectedCommunicationChannel = string.Empty;
    private bool _showAllTerminals;

    public EmulationViewModel(
        LoggingService logger,
        WizardNavigationService navigationService,
        ProcessManager processManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
        _processManager = processManager ?? throw new ArgumentNullException(nameof(processManager));

        // Initialize collections
        SourceCores = new ObservableCollection<CoreSelectionItem>();
        DestinationCores = new ObservableCollection<CoreSelectionItem>();
        CommunicationChannels = new ObservableCollection<CommunicationChannel>();
        TerminalOutput = new ObservableCollection<TerminalLine>();

        // Initialize core selections
        InitializeCoreSelections();

        // Initialize commands
        BackCommand = ReactiveCommand.Create(NavigateBack);
        StartEmulationCommand = ReactiveCommand.CreateFromTask(StartEmulationAsync, 
            this.WhenAnyValue(x => x.CanStartEmulation));
        StopEmulationCommand = ReactiveCommand.CreateFromTask(StopEmulationAsync,
            this.WhenAnyValue(x => x.IsEmulationRunning));
        SendMessageCommand = ReactiveCommand.CreateFromTask(SendMessageAsync,
            this.WhenAnyValue(x => x.CanSendMessage));
        ShowAllCommand = ReactiveCommand.Create(ShowAllTerminals);
        ClearTerminalCommand = ReactiveCommand.Create(ClearTerminal);
        SelectChannelCommand = ReactiveCommand.Create<CommunicationChannel>(SelectChannel);

        // Set up property change handlers
        this.WhenAnyValue(x => x.Message)
            .Subscribe(_ => this.RaisePropertyChanged(nameof(CanSendMessage)));

        // Subscribe to process manager events
        _processManager.OutputReceived += OnOutputReceived;
        _processManager.ProcessStatusChanged += OnProcessStatusChanged;

        _logger.LogInfo("EmulationViewModel initialized");
    }

    #region Properties

    /// <summary>
    /// Source core selection items
    /// </summary>
    public ObservableCollection<CoreSelectionItem> SourceCores { get; }

    /// <summary>
    /// Destination core selection items
    /// </summary>
    public ObservableCollection<CoreSelectionItem> DestinationCores { get; }

    /// <summary>
    /// Active communication channels
    /// </summary>
    public ObservableCollection<CommunicationChannel> CommunicationChannels { get; }

    /// <summary>
    /// Terminal output lines
    /// </summary>
    public ObservableCollection<TerminalLine> TerminalOutput { get; }

    /// <summary>
    /// Indicates if emulation is currently running
    /// </summary>
    public bool IsEmulationRunning
    {
        get => _isEmulationRunning;
        private set => this.RaiseAndSetIfChanged(ref _isEmulationRunning, value);
    }

    /// <summary>
    /// Message to send between cores
    /// </summary>
    public string Message
    {
        get => _message;
        set => this.RaiseAndSetIfChanged(ref _message, value);
    }

    /// <summary>
    /// Currently selected communication channel
    /// </summary>
    public string SelectedCommunicationChannel
    {
        get => _selectedCommunicationChannel;
        set
        {
            this.RaiseAndSetIfChanged(ref _selectedCommunicationChannel, value);
            UpdateTerminalOutput();
        }
    }

    /// <summary>
    /// Indicates if showing all terminals
    /// </summary>
    public bool ShowAllTerminals
    {
        get => _showAllTerminals;
        private set
        {
            this.RaiseAndSetIfChanged(ref _showAllTerminals, value);
            UpdateTerminalOutput();
        }
    }

    /// <summary>
    /// Selected source cores
    /// </summary>
    public IEnumerable<CoreType> SelectedSourceCores => 
        SourceCores.Where(c => c.IsSelected).Select(c => c.CoreType);

    /// <summary>
    /// Selected destination cores
    /// </summary>
    public IEnumerable<CoreType> SelectedDestinationCores => 
        DestinationCores.Where(c => c.IsSelected).Select(c => c.CoreType);

    /// <summary>
    /// Determines if emulation can be started
    /// </summary>
    public bool CanStartEmulation => !IsEmulationRunning && HasValidConfiguration();

    /// <summary>
    /// Determines if a message can be sent
    /// </summary>
    public bool CanSendMessage => IsEmulationRunning &&
        SelectedSourceCores.Any() &&
        SelectedDestinationCores.Any() &&
        !string.IsNullOrWhiteSpace(Message);

    /// <summary>
    /// Terminal output as formatted text
    /// </summary>
    public string TerminalOutputText
    {
        get
        {
            var sb = new StringBuilder();
            foreach (var line in TerminalOutput)
            {
                if (line != null && !string.IsNullOrEmpty(line.FormattedText))
                {
                    sb.AppendLine(line.FormattedText);
                }
            }
            return sb.ToString();
        }
    }

    #endregion

    #region Commands

    /// <summary>
    /// Command to navigate to the previous step
    /// </summary>
    public ReactiveCommand<Unit, Unit> BackCommand { get; }

    /// <summary>
    /// Command to start emulation
    /// </summary>
    public ReactiveCommand<Unit, Unit> StartEmulationCommand { get; }

    /// <summary>
    /// Command to stop emulation
    /// </summary>
    public ReactiveCommand<Unit, Unit> StopEmulationCommand { get; }

    /// <summary>
    /// Command to send message
    /// </summary>
    public ReactiveCommand<Unit, Unit> SendMessageCommand { get; }

    /// <summary>
    /// Command to show all terminals
    /// </summary>
    public ReactiveCommand<Unit, Unit> ShowAllCommand { get; }

    /// <summary>
    /// Command to clear terminal
    /// </summary>
    public ReactiveCommand<Unit, Unit> ClearTerminalCommand { get; }

    /// <summary>
    /// Command to select a communication channel
    /// </summary>
    public ReactiveCommand<CommunicationChannel, Unit> SelectChannelCommand { get; }

    #endregion

    #region Private Methods

    /// <summary>
    /// Initializes core selection items
    /// </summary>
    private void InitializeCoreSelections()
    {
        // Add "All" option first
        SourceCores.Add(new CoreSelectionItem(CoreType.All, this, true));
        DestinationCores.Add(new CoreSelectionItem(CoreType.All, this, false));

        // Add individual cores
        foreach (var coreType in CoreTypeExtensions.GetIndividualCores())
        {
            SourceCores.Add(new CoreSelectionItem(coreType, this, true));
            DestinationCores.Add(new CoreSelectionItem(coreType, this, false));
        }
    }

    /// <summary>
    /// Updates communication channels based on selected cores
    /// </summary>
    public void UpdateCommunicationChannels()
    {
        CommunicationChannels.Clear();

        var sourceCores = SelectedSourceCores.ToList();
        var destCores = SelectedDestinationCores.ToList();

        // Expand "All" selections
        if (sourceCores.Contains(CoreType.All))
        {
            sourceCores = CoreTypeExtensions.GetIndividualCores().ToList();
        }

        if (destCores.Contains(CoreType.All))
        {
            destCores = CoreTypeExtensions.GetIndividualCores().ToList();
        }

        // Create communication channels
        foreach (var source in sourceCores)
        {
            foreach (var dest in destCores)
            {
                if (source != dest) // Don't create self-communication
                {
                    var channel = new CommunicationChannel(source, dest);
                    CommunicationChannels.Add(channel);
                }
            }
        }

        _logger.LogInfo($"Updated communication channels: {CommunicationChannels.Count} channels");
    }

    /// <summary>
    /// Checks if there's a valid configuration for emulation
    /// </summary>
    private bool HasValidConfiguration()
    {
        // This would check if the configuration from previous steps is valid
        // For now, return true as validation is done in previous steps
        return true;
    }

    /// <summary>
    /// Updates terminal output based on selected channel or show all
    /// </summary>
    private void UpdateTerminalOutput()
    {
        // This would filter terminal output based on selected communication channel
        // Implementation depends on how terminal output is structured
        _logger.LogInfo($"Updated terminal output for channel: {SelectedCommunicationChannel}, ShowAll: {ShowAllTerminals}");
    }

    /// <summary>
    /// Navigates to the previous step
    /// </summary>
    private void NavigateBack()
    {
        _logger.LogInfo("Navigating back from emulation step");

        if (!_navigationService.NavigatePrevious())
        {
            _logger.LogError("Failed to navigate to previous step");
        }
    }

    /// <summary>
    /// Starts emulation
    /// </summary>
    private async Task StartEmulationAsync()
    {
        try
        {
            _logger.LogInfo("Starting emulation");
            IsEmulationRunning = true;

            // This would start the actual emulation processes
            // Implementation depends on the process manager

            _logger.LogInfo("Emulation started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError("Error starting emulation", ex);
            IsEmulationRunning = false;
        }
    }

    /// <summary>
    /// Stops emulation
    /// </summary>
    private async Task StopEmulationAsync()
    {
        try
        {
            _logger.LogInfo("Stopping emulation");

            // This would stop the actual emulation processes
            // Implementation depends on the process manager

            IsEmulationRunning = false;
            _logger.LogInfo("Emulation stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError("Error stopping emulation", ex);
        }
    }

    /// <summary>
    /// Sends message between selected cores
    /// </summary>
    private async Task SendMessageAsync()
    {
        try
        {
            if (!CanSendMessage)
            {
                _logger.LogWarning("Cannot send message - validation failed");
                return;
            }

            _logger.LogInfo($"Sending message: {Message}");

            // This would send the message to the selected cores
            // Implementation depends on the process manager

            // Clear message after sending
            Message = string.Empty;

            _logger.LogInfo("Message sent successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError("Error sending message", ex);
        }
    }

    /// <summary>
    /// Shows all terminal outputs
    /// </summary>
    private void ShowAllTerminals()
    {
        _logger.LogInfo("Showing all terminal outputs");
        ShowAllTerminals = true;
        SelectedCommunicationChannel = string.Empty;
    }

    /// <summary>
    /// Clears terminal output
    /// </summary>
    private void ClearTerminal()
    {
        _logger.LogInfo("Clearing terminal output");
        TerminalOutput.Clear();
        this.RaisePropertyChanged(nameof(TerminalOutputText));
    }

    /// <summary>
    /// Selects a communication channel
    /// </summary>
    private void SelectChannel(CommunicationChannel channel)
    {
        _logger.LogInfo($"Selected communication channel: {channel.DisplayName}");
        SelectedCommunicationChannel = channel.DisplayName;
        ShowAllTerminals = false;
    }

    /// <summary>
    /// Handles output received from processes
    /// </summary>
    private void OnOutputReceived(object? sender, TerminalLine line)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            TerminalOutput.Add(line);
            this.RaisePropertyChanged(nameof(TerminalOutputText));
        });
    }

    /// <summary>
    /// Handles process status changes
    /// </summary>
    private void OnProcessStatusChanged(object? sender, ProcessStatusChangedEventArgs e)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            IsEmulationRunning = _processManager.IsAnyProcessRunning;
        });
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// Sets the configuration for emulation
    /// </summary>
    public void SetConfiguration(ConfigurationSettings configuration)
    {
        _logger.LogInfo("Setting emulation configuration");
        // This would configure the emulation based on the provided settings
    }

    #endregion
}

/// <summary>
/// Represents a core selection item for the UI
/// </summary>
public class CoreSelectionItem : ReactiveObject
{
    private readonly EmulationViewModel _parent;
    private readonly bool _isSource;
    private bool _isSelected;

    public CoreSelectionItem(CoreType coreType, EmulationViewModel parent, bool isSource)
    {
        CoreType = coreType;
        _parent = parent;
        _isSource = isSource;
    }

    /// <summary>
    /// The core type
    /// </summary>
    public CoreType CoreType { get; }

    /// <summary>
    /// Display name of the core
    /// </summary>
    public string DisplayName => CoreType.GetDisplayName();

    /// <summary>
    /// Indicates if this core is selected
    /// </summary>
    public bool IsSelected
    {
        get => _isSelected;
        set
        {
            this.RaiseAndSetIfChanged(ref _isSelected, value);
            _parent.UpdateCommunicationChannels();
        }
    }
}

/// <summary>
/// Represents a communication channel between two cores
/// </summary>
public class CommunicationChannel
{
    public CommunicationChannel(CoreType source, CoreType destination)
    {
        Source = source;
        Destination = destination;
        DisplayName = $"{source.GetDisplayName()} <-> {destination.GetDisplayName()}";
    }

    /// <summary>
    /// Source core
    /// </summary>
    public CoreType Source { get; }

    /// <summary>
    /// Destination core
    /// </summary>
    public CoreType Destination { get; }

    /// <summary>
    /// Display name for the communication channel
    /// </summary>
    public string DisplayName { get; }
}
