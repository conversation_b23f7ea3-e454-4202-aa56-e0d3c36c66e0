using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using Avalonia.Platform.Storage;
using ReactiveUI;
using TDA4_Emulator.Models;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.ViewModels;

/// <summary>
/// ViewModel for Step 2: Configuration
/// </summary>
public class ConfigurationViewModel : ViewModelBase
{
    private readonly LoggingService _logger;
    private readonly WizardNavigationService _navigationService;
    private readonly ConfigurationService _configurationService;

    private ConfigurationSettings _configuration = new();
    private readonly Dictionary<CoreType, string> _pathErrors = new();

    public ConfigurationViewModel(
        LoggingService logger, 
        WizardNavigationService navigationService,
        ConfigurationService configurationService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
        _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));

        // Initialize core groups
        InitializeCoreGroups();

        // Initialize commands
        BackCommand = ReactiveCommand.Create(NavigateBack);
        NextCommand = ReactiveCommand.Create(NavigateNext, this.WhenAnyValue(x => x.CanNavigateNext));
        BrowseBinaryCommand = ReactiveCommand.CreateFromTask<CoreType>(BrowseBinaryAsync);
        BrowseQemuBinaryCommand = ReactiveCommand.CreateFromTask(BrowseQemuBinaryAsync);
        ResetConfigurationCommand = ReactiveCommand.CreateFromTask(ResetConfigurationAsync);
        LoadConfigurationCommand = ReactiveCommand.CreateFromTask(LoadConfigurationAsync);

        // Set up property change handlers for validation
        this.WhenAnyValue(x => x.QemuBinaryPath)
            .Subscribe(_ => ValidateStep());

        this.WhenAnyValue(x => x.AutoSaveConfiguration)
            .Subscribe(_ => ValidateStep());

        this.WhenAnyValue(x => x.ProcessTimeoutSeconds)
            .Subscribe(_ => ValidateStep());

        // Load initial configuration
        _ = Task.Run(LoadConfigurationAsync);

        _logger.LogInfo("ConfigurationViewModel initialized");
    }

    #region Properties

    /// <summary>
    /// ARM A72 core configurations
    /// </summary>
    public ObservableCollection<CoreConfigurationItem> A72Cores { get; } = new();

    /// <summary>
    /// ARM MCU R5F core configurations
    /// </summary>
    public ObservableCollection<CoreConfigurationItem> McuR5FCores { get; } = new();

    /// <summary>
    /// ARM Main R5F core configurations
    /// </summary>
    public ObservableCollection<CoreConfigurationItem> MainR5FCores { get; } = new();

    /// <summary>
    /// DSP C7x core configurations
    /// </summary>
    public ObservableCollection<CoreConfigurationItem> C7xCores { get; } = new();

    /// <summary>
    /// QEMU binary path
    /// </summary>
    public string QemuBinaryPath
    {
        get => _configuration.QemuBinaryPath;
        set
        {
            _configuration.QemuBinaryPath = value;
            this.RaisePropertyChanged();
            ValidateQemuPath();
        }
    }

    /// <summary>
    /// Auto-save configuration setting
    /// </summary>
    public bool AutoSaveConfiguration
    {
        get => _configuration.AutoSaveConfiguration;
        set
        {
            _configuration.AutoSaveConfiguration = value;
            this.RaisePropertyChanged();
        }
    }

    /// <summary>
    /// Process timeout in seconds
    /// </summary>
    public int ProcessTimeoutSeconds
    {
        get => _configuration.ProcessTimeoutSeconds;
        set
        {
            _configuration.ProcessTimeoutSeconds = value;
            this.RaisePropertyChanged();
        }
    }

    /// <summary>
    /// QEMU path validation error
    /// </summary>
    public string QemuPathError { get; private set; } = string.Empty;

    /// <summary>
    /// Determines if the step is valid and can navigate to next
    /// </summary>
    public bool CanNavigateNext => IsStepValid();

    #endregion

    #region Commands

    /// <summary>
    /// Command to navigate to the previous step
    /// </summary>
    public ReactiveCommand<Unit, Unit> BackCommand { get; }

    /// <summary>
    /// Command to navigate to the next step
    /// </summary>
    public ReactiveCommand<Unit, Unit> NextCommand { get; }

    /// <summary>
    /// Command to browse for a binary file
    /// </summary>
    public ReactiveCommand<CoreType, Unit> BrowseBinaryCommand { get; }

    /// <summary>
    /// Command to browse for QEMU binary
    /// </summary>
    public ReactiveCommand<Unit, Unit> BrowseQemuBinaryCommand { get; }

    /// <summary>
    /// Command to reset configuration
    /// </summary>
    public ReactiveCommand<Unit, Unit> ResetConfigurationCommand { get; }

    /// <summary>
    /// Command to load configuration
    /// </summary>
    public ReactiveCommand<Unit, Unit> LoadConfigurationCommand { get; }

    #endregion

    #region Private Methods

    /// <summary>
    /// Initializes the core group collections
    /// </summary>
    private void InitializeCoreGroups()
    {
        // ARM A72 Cores
        foreach (var coreType in CoreTypeExtensions.GetA72Cores())
        {
            A72Cores.Add(new CoreConfigurationItem(coreType, this));
        }

        // ARM MCU R5F Cores
        foreach (var coreType in CoreTypeExtensions.GetMcuR5FCores())
        {
            McuR5FCores.Add(new CoreConfigurationItem(coreType, this));
        }

        // ARM Main R5F Cores
        foreach (var coreType in CoreTypeExtensions.GetMainR5FCores())
        {
            MainR5FCores.Add(new CoreConfigurationItem(coreType, this));
        }

        // DSP C7x Cores
        foreach (var coreType in CoreTypeExtensions.GetC7xCores())
        {
            C7xCores.Add(new CoreConfigurationItem(coreType, this));
        }
    }

    /// <summary>
    /// Validates the QEMU binary path
    /// </summary>
    private void ValidateQemuPath()
    {
        if (string.IsNullOrWhiteSpace(QemuBinaryPath))
        {
            QemuPathError = string.Empty;
        }
        else if (!File.Exists(QemuBinaryPath))
        {
            QemuPathError = "File does not exist";
        }
        else if (!IsExecutableFile(QemuBinaryPath))
        {
            QemuPathError = GetExecutableErrorMessage();
        }
        else
        {
            QemuPathError = string.Empty;
        }

        this.RaisePropertyChanged(nameof(QemuPathError));
        ValidateStep();
    }

    /// <summary>
    /// Validates a core binary path
    /// </summary>
    public void ValidateCorePath(CoreType coreType, string path)
    {
        if (string.IsNullOrWhiteSpace(path))
        {
            _pathErrors.Remove(coreType);
        }
        else if (!File.Exists(path))
        {
            _pathErrors[coreType] = "File does not exist";
        }
        else if (!IsExecutableFile(path))
        {
            _pathErrors[coreType] = GetExecutableErrorMessage();
        }
        else
        {
            _pathErrors.Remove(coreType);
        }

        ValidateStep();
    }

    /// <summary>
    /// Gets the validation error for a core path
    /// </summary>
    public string GetCorePathError(CoreType coreType)
    {
        return _pathErrors.TryGetValue(coreType, out var error) ? error : string.Empty;
    }

    /// <summary>
    /// Validates the current step
    /// </summary>
    private void ValidateStep()
    {
        var isValid = IsStepValid();
        _navigationService.SetStepValidation(WizardStep.Configuration, isValid);
        
        // Update UI
        this.RaisePropertyChanged(nameof(CanNavigateNext));
        
        _logger.LogInfo($"Configuration step validation: {isValid}");
    }

    /// <summary>
    /// Determines if the step is valid
    /// </summary>
    private bool IsStepValid()
    {
        // Step is valid if at least one valid binary path is provided
        var hasValidPath = GetAllCoreItems().Any(item => 
            !string.IsNullOrWhiteSpace(item.BinaryPath) && 
            !_pathErrors.ContainsKey(item.CoreType));

        // QEMU path is optional but if provided must be valid
        var qemuValid = string.IsNullOrWhiteSpace(QemuBinaryPath) || string.IsNullOrEmpty(QemuPathError);

        // Process timeout must be valid
        var timeoutValid = ProcessTimeoutSeconds > 0 && ProcessTimeoutSeconds <= 3600;

        return hasValidPath && qemuValid && timeoutValid;
    }

    /// <summary>
    /// Gets all core configuration items
    /// </summary>
    private IEnumerable<CoreConfigurationItem> GetAllCoreItems()
    {
        return A72Cores.Concat(McuR5FCores).Concat(MainR5FCores).Concat(C7xCores);
    }

    /// <summary>
    /// Checks if a file is executable
    /// </summary>
    private bool IsExecutableFile(string filePath)
    {
        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".exe" || extension == ".bat" || extension == ".cmd";
        }
        else
        {
            // On Unix-like systems, accept common executable patterns
            var fileName = Path.GetFileName(filePath).ToLowerInvariant();
            var fileExtension = Path.GetExtension(filePath).ToLowerInvariant();

            // Accept files with no extension (common for Linux executables)
            if (string.IsNullOrEmpty(fileExtension))
                return true;

            // Accept common executable extensions
            return fileExtension == ".bin" || fileExtension == ".elf" || fileExtension == ".out";
        }
    }

    /// <summary>
    /// Gets the executable error message for the current platform
    /// </summary>
    private string GetExecutableErrorMessage()
    {
        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            return "File must be .exe, .bat, or .cmd";
        }
        else
        {
            return "File must be executable (try: chmod +x filename)";
        }
    }

    /// <summary>
    /// Navigates to the previous step
    /// </summary>
    private void NavigateBack()
    {
        _logger.LogInfo("Navigating back from configuration step");

        if (!_navigationService.NavigatePrevious())
        {
            _logger.LogError("Failed to navigate to previous step");
        }
    }

    /// <summary>
    /// Navigates to the next step
    /// </summary>
    private void NavigateNext()
    {
        if (!CanNavigateNext)
        {
            _logger.LogWarning("Cannot navigate to next step - validation failed");
            return;
        }

        _logger.LogInfo("Navigating to next step from configuration");

        // Save configuration if auto-save is enabled
        if (AutoSaveConfiguration)
        {
            _ = Task.Run(SaveConfigurationAsync);
        }

        if (!_navigationService.NavigateNext())
        {
            _logger.LogError("Failed to navigate to next step");
        }
    }

    /// <summary>
    /// Browses for a binary file for a specific core
    /// </summary>
    private async Task BrowseBinaryAsync(CoreType coreType)
    {
        try
        {
            _logger.LogInfo($"Browsing for {coreType.GetDisplayName()} binary");

            // Get the main window to access the storage provider
            var mainWindow = Avalonia.Application.Current?.ApplicationLifetime is
                Avalonia.Controls.ApplicationLifetimes.IClassicDesktopStyleApplicationLifetime desktop
                ? desktop.MainWindow : null;

            if (mainWindow?.StorageProvider != null)
            {
                var files = await mainWindow.StorageProvider.OpenFilePickerAsync(new FilePickerOpenOptions
                {
                    Title = $"Select {coreType.GetDisplayName()} Binary",
                    AllowMultiple = false,
                    FileTypeFilter = GetExecutableFileTypes()
                });

                if (files.Count > 0)
                {
                    var selectedPath = files[0].Path.LocalPath;

                    // Update the binary path for the core
                    var coreItem = GetAllCoreItems().FirstOrDefault(item => item.CoreType == coreType);
                    if (coreItem != null)
                    {
                        coreItem.BinaryPath = selectedPath;
                        _configuration.BinaryPaths.SetPath(coreType, selectedPath);
                    }

                    _logger.LogInfo($"Binary selected for {coreType}: {selectedPath}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error browsing for {coreType} binary", ex);
        }
    }

    /// <summary>
    /// Browses for QEMU binary
    /// </summary>
    private async Task BrowseQemuBinaryAsync()
    {
        try
        {
            _logger.LogInfo("Browsing for QEMU binary");

            var mainWindow = Avalonia.Application.Current?.ApplicationLifetime is
                Avalonia.Controls.ApplicationLifetimes.IClassicDesktopStyleApplicationLifetime desktop
                ? desktop.MainWindow : null;

            if (mainWindow?.StorageProvider != null)
            {
                var files = await mainWindow.StorageProvider.OpenFilePickerAsync(new FilePickerOpenOptions
                {
                    Title = "Select QEMU Binary",
                    AllowMultiple = false,
                    FileTypeFilter = GetExecutableFileTypes()
                });

                if (files.Count > 0)
                {
                    QemuBinaryPath = files[0].Path.LocalPath;
                    _logger.LogInfo($"QEMU binary selected: {QemuBinaryPath}");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("Error browsing for QEMU binary", ex);
        }
    }

    /// <summary>
    /// Gets executable file types for file picker
    /// </summary>
    private List<FilePickerFileType> GetExecutableFileTypes()
    {
        var fileTypes = new List<FilePickerFileType>();

        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            fileTypes.Add(new FilePickerFileType("Executable Files")
            {
                Patterns = new[] { "*.exe", "*.bat", "*.cmd" }
            });
        }
        else
        {
            fileTypes.Add(new FilePickerFileType("Executable Files")
            {
                Patterns = new[] { "*" }
            });

            fileTypes.Add(new FilePickerFileType("Binary Files")
            {
                Patterns = new[] { "*.bin", "*.elf", "*.out" }
            });
        }

        fileTypes.Add(new FilePickerFileType("All Files")
        {
            Patterns = new[] { "*" }
        });

        return fileTypes;
    }

    /// <summary>
    /// Resets configuration to defaults
    /// </summary>
    private async Task ResetConfigurationAsync()
    {
        try
        {
            _logger.LogInfo("Resetting configuration to defaults");

            var success = await _configurationService.ResetToDefaultAsync();
            if (success)
            {
                await LoadConfigurationAsync();
                _logger.LogInfo("Configuration reset successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("Error resetting configuration", ex);
        }
    }

    /// <summary>
    /// Loads configuration from file
    /// </summary>
    private async Task LoadConfigurationAsync()
    {
        try
        {
            var appConfig = await _configurationService.LoadConfigurationAsync();
            _configuration = ConvertFromApplicationConfiguration(appConfig);

            // Update UI
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                // Update core items
                foreach (var coreItem in GetAllCoreItems())
                {
                    coreItem.BinaryPath = _configuration.BinaryPaths.GetPath(coreItem.CoreType);
                }

                // Update properties
                this.RaisePropertyChanged(nameof(QemuBinaryPath));
                this.RaisePropertyChanged(nameof(AutoSaveConfiguration));
                this.RaisePropertyChanged(nameof(ProcessTimeoutSeconds));

                // Validate all paths
                ValidateQemuPath();
                foreach (var coreItem in GetAllCoreItems())
                {
                    ValidateCorePath(coreItem.CoreType, coreItem.BinaryPath);
                }
            });

            _logger.LogInfo("Configuration loaded successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError("Error loading configuration", ex);
        }
    }

    /// <summary>
    /// Saves current configuration
    /// </summary>
    public async Task SaveConfigurationAsync()
    {
        try
        {
            // Update configuration with current values
            foreach (var coreItem in GetAllCoreItems())
            {
                _configuration.BinaryPaths.SetPath(coreItem.CoreType, coreItem.BinaryPath);
            }

            var appConfig = ConvertToApplicationConfiguration(_configuration);
            var success = await _configurationService.SaveConfigurationAsync(appConfig);
            if (success)
            {
                _logger.LogInfo("Configuration saved successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("Error saving configuration", ex);
        }
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// Gets the current configuration
    /// </summary>
    public ConfigurationSettings GetConfiguration()
    {
        // Update configuration with current values
        foreach (var coreItem in GetAllCoreItems())
        {
            _configuration.BinaryPaths.SetPath(coreItem.CoreType, coreItem.BinaryPath);
        }

        return _configuration;
    }

    /// <summary>
    /// Sets the configuration
    /// </summary>
    public void SetConfiguration(ConfigurationSettings configuration)
    {
        _configuration = configuration ?? new ConfigurationSettings();

        // Update UI
        foreach (var coreItem in GetAllCoreItems())
        {
            coreItem.BinaryPath = _configuration.BinaryPaths.GetPath(coreItem.CoreType);
        }

        this.RaisePropertyChanged(nameof(QemuBinaryPath));
        this.RaisePropertyChanged(nameof(AutoSaveConfiguration));
        this.RaisePropertyChanged(nameof(ProcessTimeoutSeconds));

        ValidateStep();
    }

    /// <summary>
    /// Converts ApplicationConfiguration to ConfigurationSettings
    /// </summary>
    private ConfigurationSettings ConvertFromApplicationConfiguration(ApplicationConfiguration appConfig)
    {
        var config = new ConfigurationSettings
        {
            SelectedModel = TDA4Model.TDA4VM, // Default, will be set by ModelSelectionViewModel
            SelectedTestMode = TestMode.IPC, // Default, will be set by ModelSelectionViewModel
            QemuBinaryPath = appConfig.QemuBinaryPaths.GetPath(),
            AutoSaveConfiguration = appConfig.Settings.AutoSaveConfiguration,
            ProcessTimeoutSeconds = appConfig.Settings.ProcessTimeoutSeconds
        };

        // Map legacy binary paths to new structure
        config.BinaryPaths.SetPath(CoreType.R5F, appConfig.R5FBinaryPaths);
        config.BinaryPaths.SetPath(CoreType.A72, appConfig.A72BinaryPaths);
        config.BinaryPaths.SetPath(CoreType.C7x, appConfig.C7xBinaryPaths);

        return config;
    }

    /// <summary>
    /// Converts ConfigurationSettings to ApplicationConfiguration
    /// </summary>
    private ApplicationConfiguration ConvertToApplicationConfiguration(ConfigurationSettings config)
    {
        return new ApplicationConfiguration
        {
            QemuBinaryPaths = new QemuBinaryPaths { Path = config.QemuBinaryPath },
            R5FBinaryPaths = config.BinaryPaths.GetPath(CoreType.R5F),
            A72BinaryPaths = config.BinaryPaths.GetPath(CoreType.A72),
            C7xBinaryPaths = config.BinaryPaths.GetPath(CoreType.C7x),
            Settings = new ApplicationSettings
            {
                AutoSaveConfiguration = config.AutoSaveConfiguration,
                ProcessTimeoutSeconds = config.ProcessTimeoutSeconds,
                ValidateBinaryPathsOnStartup = true
            }
        };
    }

    #endregion
}

/// <summary>
/// Represents a core configuration item for the UI
/// </summary>
public class CoreConfigurationItem : ReactiveObject
{
    private readonly ConfigurationViewModel _parent;
    private string _binaryPath = string.Empty;

    public CoreConfigurationItem(CoreType coreType, ConfigurationViewModel parent)
    {
        CoreType = coreType;
        _parent = parent;
    }

    /// <summary>
    /// The core type
    /// </summary>
    public CoreType CoreType { get; }

    /// <summary>
    /// Display name of the core
    /// </summary>
    public string DisplayName => CoreType.GetDisplayName();

    /// <summary>
    /// Binary path for this core
    /// </summary>
    public string BinaryPath
    {
        get => _binaryPath;
        set
        {
            this.RaiseAndSetIfChanged(ref _binaryPath, value);
            _parent.ValidateCorePath(CoreType, value);
        }
    }

    /// <summary>
    /// Validation error for the binary path
    /// </summary>
    public string PathError => _parent.GetCorePathError(CoreType);

    /// <summary>
    /// Indicates if the path is valid
    /// </summary>
    public bool IsPathValid => string.IsNullOrEmpty(PathError) && !string.IsNullOrWhiteSpace(BinaryPath);
}
