﻿using System;
using System.Reactive;
using ReactiveUI;
using TDA4_Emulator.Models;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.ViewModels;

/// <summary>
/// Main window view model that acts as the wizard container
/// </summary>
public class MainWindowViewModel : ViewModelBase
{
    private readonly LoggingService _logger;
    private readonly WizardNavigationService _navigationService;
    private readonly ConfigurationService _configurationService;
    private readonly ProcessManager _processManager;

    // Step ViewModels
    private readonly ModelSelectionViewModel _modelSelectionViewModel;
    private readonly ConfigurationViewModel _configurationViewModel;
    private readonly EmulationViewModel _emulationViewModel;

    // UI state properties
    private WizardStep _currentStep = WizardStep.ModelSelection;
    private string _statusMessage = "Ready";
    private ViewModelBase? _currentStepViewModel;



    public MainWindowViewModel()
    {
        _logger = LoggingService.Instance;
        _configurationService = new ConfigurationService(_logger);
        _processManager = new ProcessManager();
        _navigationService = new WizardNavigationService(_logger);

        // Initialize step view models
        _modelSelectionViewModel = new ModelSelectionViewModel(_logger, _navigationService);
        _configurationViewModel = new ConfigurationViewModel(_logger, _navigationService, _configurationService);
        _emulationViewModel = new EmulationViewModel(_logger, _navigationService, _processManager);

        // Initialize commands
        ShowHelpCommand = ReactiveCommand.Create(ShowHelp);

        // Subscribe to navigation service events
        _navigationService.CurrentStepChanged += OnCurrentStepChanged;

        // Set initial step
        UpdateCurrentStep();

        _logger.LogInfo("TDA4 Emulator Control Panel (Wizard) initialized");
    }

    #region Properties

    /// <summary>
    /// Current wizard step
    /// </summary>
    public WizardStep CurrentStep
    {
        get => _currentStep;
        private set => this.RaiseAndSetIfChanged(ref _currentStep, value);
    }

    /// <summary>
    /// Current step view model
    /// </summary>
    public ViewModelBase? CurrentStepViewModel
    {
        get => _currentStepViewModel;
        private set => this.RaiseAndSetIfChanged(ref _currentStepViewModel, value);
    }

    /// <summary>
    /// Current status message
    /// </summary>
    public string StatusMessage
    {
        get => _statusMessage;
        set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
    }

    /// <summary>
    /// Model selection view model
    /// </summary>
    public ModelSelectionViewModel ModelSelectionViewModel => _modelSelectionViewModel;

    /// <summary>
    /// Configuration view model
    /// </summary>
    public ConfigurationViewModel ConfigurationViewModel => _configurationViewModel;

    /// <summary>
    /// Emulation view model
    /// </summary>
    public EmulationViewModel EmulationViewModel => _emulationViewModel;

    /// <summary>
    /// Navigation service
    /// </summary>
    public WizardNavigationService NavigationService => _navigationService;

    /// <summary>
    /// Indicates if the current step is Step 1 (Model Selection)
    /// </summary>
    public bool IsStep1 => CurrentStep == WizardStep.ModelSelection;

    /// <summary>
    /// Indicates if the current step is Step 2 (Configuration)
    /// </summary>
    public bool IsStep2 => CurrentStep == WizardStep.Configuration;

    /// <summary>
    /// Indicates if the current step is Step 3 (Emulation)
    /// </summary>
    public bool IsStep3 => CurrentStep == WizardStep.Emulation;

    #endregion

    #region Commands

    /// <summary>
    /// Command to show help dialog
    /// </summary>
    public ReactiveCommand<Unit, Unit> ShowHelpCommand { get; }

    #endregion

    #region Private Methods

    /// <summary>
    /// Handles wizard step changes
    /// </summary>
    private void OnCurrentStepChanged(object? sender, WizardStepChangedEventArgs e)
    {
        CurrentStep = e.NewStep;
        UpdateCurrentStep();
        UpdateStatusMessage();
    }

    /// <summary>
    /// Updates the current step view model
    /// </summary>
    private void UpdateCurrentStep()
    {
        CurrentStepViewModel = CurrentStep switch
        {
            WizardStep.ModelSelection => _modelSelectionViewModel,
            WizardStep.Configuration => _configurationViewModel,
            WizardStep.Emulation => _emulationViewModel,
            _ => null
        };

        // Update step indicators
        this.RaisePropertyChanged(nameof(IsStep1));
        this.RaisePropertyChanged(nameof(IsStep2));
        this.RaisePropertyChanged(nameof(IsStep3));

        _logger.LogInfo($"Updated current step to: {CurrentStep}");
    }

    /// <summary>
    /// Updates the status message based on current step
    /// </summary>
    private void UpdateStatusMessage()
    {
        StatusMessage = CurrentStep switch
        {
            WizardStep.ModelSelection => "Select TDA4 model and test mode",
            WizardStep.Configuration => "Configure core binary paths and settings",
            WizardStep.Emulation => "Control emulation and inter-processor communication",
            _ => "Ready"
        };
    }

    /// <summary>
    /// Shows the help dialog
    /// </summary>
    private void ShowHelp()
    {
        try
        {
            StatusMessage = "Help dialog opened";
            _logger.LogInfo("Help dialog requested");

            var helpDialog = new TDA4_Emulator.Views.HelpDialog();

            // Get the main window to show dialog
            var mainWindow = Avalonia.Application.Current?.ApplicationLifetime is
                Avalonia.Controls.ApplicationLifetimes.IClassicDesktopStyleApplicationLifetime desktop
                ? desktop.MainWindow : null;

            if (mainWindow != null)
            {
                helpDialog.ShowDialog(mainWindow);
            }
            else
            {
                helpDialog.Show();
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error showing help";
            _logger.LogError("Error showing help", ex);
        }
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// Gets the current configuration from all steps
    /// </summary>
    public ConfigurationSettings GetCurrentConfiguration()
    {
        var config = _configurationViewModel.GetConfiguration();
        var (model, testMode) = _modelSelectionViewModel.GetConfiguration();

        config.SelectedModel = model;
        config.SelectedTestMode = testMode;

        return config;
    }

    /// <summary>
    /// Resets the wizard to the beginning
    /// </summary>
    public void ResetWizard()
    {
        _logger.LogInfo("Resetting wizard to beginning");

        _navigationService.Reset();
        _modelSelectionViewModel.Reset();

        StatusMessage = "Wizard reset to beginning";
    }

    #endregion
}



    /// <summary>
    /// Terminal output for R5F core as formatted text (filtered from QEMU output)
    /// </summary>
    public string R5FTerminalOutput
    {
        get
        {
            try
            {
                var lines = _qemuController.TerminalOutput.Where(line => line.SourceCore == CoreType.R5F || line.SourceCore == CoreType.All);
                return ConvertTerminalLinesToText(new ObservableCollection<TerminalLine>(lines));
            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting R5F terminal output", ex);
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Terminal output for A72 core as formatted text (filtered from QEMU output)
    /// </summary>
    public string A72TerminalOutput
    {
        get
        {
            try
            {
                var lines = _qemuController.TerminalOutput.Where(line => line.SourceCore == CoreType.A72 || line.SourceCore == CoreType.All);
                return ConvertTerminalLinesToText(new ObservableCollection<TerminalLine>(lines));
            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting A72 terminal output", ex);
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Terminal output for C7x core as formatted text (from direct C7x process)
    /// </summary>
    public string C7xTerminalOutput
    {
        get
        {
            try
            {
                // Get output from direct C7x controller (not QEMU)
                return ConvertTerminalLinesToText(_c7xController.TerminalOutput);
            }
            catch (Exception ex)
            {
                _logger.LogError("Error getting C7x terminal output", ex);
                return string.Empty;
            }
        }
    }

    /// <summary>
    /// Determines if emulation can be started (supports hybrid emulation modes)
    /// </summary>
    public bool CanStartEmulation => !IsEmulationRunning && (
        (R5FPathValid && A72PathValid) ||  // QEMU emulation (R5F + A72)
        C7xPathValid ||                    // Direct C7x execution
        (R5FPathValid && A72PathValid && C7xPathValid)  // Hybrid (QEMU + C7x)
    );

    /// <summary>
    /// Determines if IPC message can be sent
    /// </summary>
    public bool CanSendIpc
    {
        get
        {
            var isRunning = IsEmulationRunning;
            var hasSourceCores = SelectedSourceCores.Any();
            var hasDestCores = SelectedDestinationCores.Any();
            var hasMessage = !string.IsNullOrWhiteSpace(IpcMessage);

            return isRunning && hasSourceCores && hasDestCores && hasMessage;
        }
    }

    #endregion

    #region Commands

    public ReactiveCommand<Unit, Unit> StartEmulationCommand { get; }
    public ReactiveCommand<Unit, Unit> StopEmulationCommand { get; }
    public ReactiveCommand<Unit, Unit> SendIpcCommand { get; }
    public ReactiveCommand<Unit, Unit> BrowseR5FBinaryCommand { get; }
    public ReactiveCommand<Unit, Unit> BrowseA72BinaryCommand { get; }
    public ReactiveCommand<Unit, Unit> BrowseC7xBinaryCommand { get; }
    public ReactiveCommand<CoreType, Unit> ClearTerminalCommand { get; }
    public ReactiveCommand<Unit, Unit> ShowHelpCommand { get; }
    public ReactiveCommand<Unit, Unit> ResetConfigurationCommand { get; }
    public ReactiveCommand<Unit, Unit> SaveConfigurationCommand { get; }


    #endregion

    #region Command Implementations

    private async Task StartEmulationAsync()
    {
        try
        {
            StatusMessage = "Starting hybrid emulation...";
            _logger.LogInfo("Starting TDA4 hybrid emulation");

            bool qemuStarted = false;
            bool c7xStarted = false;
            var startedProcesses = new List<string>();

            // Determine emulation mode and start appropriate processes
            bool shouldStartQemu = R5FPathValid && A72PathValid;
            bool shouldStartC7x = C7xPathValid;

            if (!shouldStartQemu && !shouldStartC7x)
            {
                StatusMessage = "No valid binary paths configured for emulation";
                _logger.LogWarning("Attempted to start emulation with no valid binary paths");
                return;
            }

            // Start QEMU emulation for R5F + A72 cores
            if (shouldStartQemu)
            {
                _logger.LogInfo("Starting QEMU emulation for R5F and A72 cores");

                // Get QEMU binary path (custom from config or default)
                string qemuBinaryPath;
                try
                {
                    // Get the custom QEMU path from configuration
                    var config = await _configurationService.GetConfigurationAsync();
                    var customQemuPath = config.QemuBinaryPaths.GetPath();

                    qemuBinaryPath = _qemuManager.GetQemuBinaryPath(customQemuPath);
                }
                catch (FileNotFoundException ex)
                {
                    StatusMessage = "QEMU binary not found";
                    _logger.LogError($"QEMU binary not found: {ex.Message}");
                    return;
                }

                // Configure QEMU controller (only R5F and A72)
                _qemuController.QemuBinaryPath = qemuBinaryPath;
                _qemuController.R5FBinaryPath = R5FBinaryPath;
                _qemuController.A72BinaryPath = A72BinaryPath;

                // Start QEMU process
                qemuStarted = await _qemuController.StartAsync();
                if (qemuStarted)
                {
                    startedProcesses.Add("QEMU (R5F + A72)");
                    _logger.LogInfo("QEMU emulation started successfully");
                }
                else
                {
                    _logger.LogError("Failed to start QEMU emulation");
                }
            }

            // Start direct C7x execution
            if (shouldStartC7x)
            {
                _logger.LogInfo("Starting direct C7x core execution");

                // Configure C7x controller
                _c7xController.BinaryPath = C7xBinaryPath;

                // Start C7x process
                c7xStarted = await _c7xController.StartAsync();
                if (c7xStarted)
                {
                    startedProcesses.Add("C7x (Direct)");
                    _logger.LogInfo("C7x direct execution started successfully");
                }
                else
                {
                    _logger.LogError("Failed to start C7x direct execution");
                }
            }

            // Update UI state based on results
            bool anyStarted = qemuStarted || c7xStarted;
            if (anyStarted)
            {
                IsEmulationRunning = true;
                var processesText = string.Join(" + ", startedProcesses);
                StatusMessage = $"Hybrid emulation started: {processesText}";
                _logger.LogInfo($"Hybrid emulation started successfully: {processesText}");
            }
            else
            {
                StatusMessage = "Failed to start any emulation processes";
                _logger.LogError("Failed to start any emulation processes");

                // Clean up any partially started processes
                await StopEmulationAsync();
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error starting hybrid emulation";
            _logger.LogError("Error starting hybrid emulation", ex);

            // Clean up any partially started processes
            await StopEmulationAsync();
        }
    }

    private async Task StopEmulationAsync()
    {
        try
        {
            StatusMessage = "Stopping hybrid emulation...";
            _logger.LogInfo("Stopping TDA4 hybrid emulation");

            var stopTasks = new List<Task>();
            var stoppedProcesses = new List<string>();

            // Stop QEMU process if running
            if (_qemuController.IsRunning)
            {
                _logger.LogInfo("Stopping QEMU emulation");
                stopTasks.Add(_qemuController.StopAsync());
                stoppedProcesses.Add("QEMU");
            }

            // Stop C7x process if running
            if (_c7xController.IsRunning)
            {
                _logger.LogInfo("Stopping C7x direct execution");
                stopTasks.Add(_c7xController.StopAsync());
                stoppedProcesses.Add("C7x");
            }

            // Wait for all processes to stop
            if (stopTasks.Any())
            {
                await Task.WhenAll(stopTasks);
            }

            IsEmulationRunning = false;

            if (stoppedProcesses.Any())
            {
                var processesText = string.Join(" + ", stoppedProcesses);
                StatusMessage = $"Hybrid emulation stopped: {processesText}";
                _logger.LogInfo($"Hybrid emulation stopped successfully: {processesText}");
            }
            else
            {
                StatusMessage = "No emulation processes were running";
                _logger.LogInfo("No emulation processes were running");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error stopping hybrid emulation";
            _logger.LogError("Error stopping hybrid emulation", ex);

            // Force update the running state even if there was an error
            IsEmulationRunning = false;
        }
    }

    private async Task SendIpcMessageAsync()
    {
        try
        {
            if (!IsEmulationRunning)
            {
                StatusMessage = "No emulation processes are running";
                return;
            }

            if (!SelectedSourceCores.Any())
            {
                StatusMessage = "No source cores selected for IPC command";
                return;
            }

            // Determine which processes to target based on selected source cores
            var routingResult = DetermineCommandRouting(SelectedSourceCores);

            if (!routingResult.ShouldSendToQemu && !routingResult.ShouldSendToC7x)
            {
                StatusMessage = "No running processes correspond to selected source cores";
                return;
            }

            var sendTasks = new List<Task<bool>>();
            var targetProcesses = new List<string>();
            var targetCores = new List<string>();

            // Send command to QEMU process if required and running
            if (routingResult.ShouldSendToQemu && _qemuController.IsRunning)
            {
                sendTasks.Add(_qemuController.SendCommandAsync(IpcMessage));
                targetProcesses.Add("QEMU");
                targetCores.AddRange(routingResult.QemuCores);
            }

            // Send command to C7x process if required and running
            if (routingResult.ShouldSendToC7x && _c7xController.IsRunning)
            {
                sendTasks.Add(_c7xController.SendCommandAsync(IpcMessage));
                targetProcesses.Add("C7x");
                targetCores.Add("C7x");
            }

            if (!sendTasks.Any())
            {
                var missingProcesses = new List<string>();
                if (routingResult.ShouldSendToQemu && !_qemuController.IsRunning)
                    missingProcesses.Add("QEMU");
                if (routingResult.ShouldSendToC7x && !_c7xController.IsRunning)
                    missingProcesses.Add("C7x");

                StatusMessage = $"Required processes not running: {string.Join(", ", missingProcesses)}";
                return;
            }

            // Wait for all commands to be sent
            var results = await Task.WhenAll(sendTasks);
            var successCount = results.Count(r => r);
            var totalCount = results.Length;

            if (successCount > 0)
            {
                var processesText = string.Join(" + ", targetProcesses.Take(successCount));
                var coresText = string.Join(", ", targetCores);
                StatusMessage = $"Command sent to {processesText} for cores: {coresText} ({successCount}/{totalCount})";
                var commandText = IpcMessage; // Store before clearing
                IpcMessage = string.Empty; // Clear after sending
                _logger.LogInfo($"Command sent successfully to {successCount}/{totalCount} processes for cores [{coresText}]: {commandText}");
            }
            else
            {
                StatusMessage = "Failed to send command to any processes";
                _logger.LogWarning("Failed to send command to any processes");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error sending command to processes";
            _logger.LogError("Error sending command to processes", ex);
        }
    }





    private async Task BrowseBinaryAsync(CoreType coreType)
    {
        try
        {
            StatusMessage = $"Browse for {coreType.GetDisplayName()} binary...";

            // Get the main window to access the storage provider
            var mainWindow = Avalonia.Application.Current?.ApplicationLifetime is
                Avalonia.Controls.ApplicationLifetimes.IClassicDesktopStyleApplicationLifetime desktop
                ? desktop.MainWindow : null;

            if (mainWindow?.StorageProvider != null)
            {
                var files = await mainWindow.StorageProvider.OpenFilePickerAsync(new FilePickerOpenOptions
                {
                    Title = $"Select {coreType.GetDisplayName()} Binary",
                    AllowMultiple = false,
                    FileTypeFilter = GetExecutableFileTypes()
                });

                if (files.Count > 0)
                {
                    var selectedPath = files[0].Path.LocalPath;

                    // Update the appropriate binary path property
                    switch (coreType)
                    {
                        case CoreType.R5F:
                            R5FBinaryPath = selectedPath;
                            break;
                        case CoreType.A72:
                            A72BinaryPath = selectedPath;
                            break;
                        case CoreType.C7x:
                            C7xBinaryPath = selectedPath;
                            break;
                    }

                    StatusMessage = $"{coreType.GetDisplayName()} binary selected";
                    _logger.LogInfo($"Binary selected for {coreType}: {selectedPath}");

                    // Log validation state after file selection
                    LogValidationState($"After {coreType} file selection");
                }
                else
                {
                    StatusMessage = "File selection cancelled";
                }
            }
            else
            {
                StatusMessage = "File browser not available";
                _logger.LogWarning("Storage provider not available for file browsing");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error browsing for binary";
            _logger.LogError("Error browsing for binary", ex);
        }
    }

    private List<FilePickerFileType> GetExecutableFileTypes()
    {
        var fileTypes = new List<FilePickerFileType>();

        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            // Windows executable file types
            fileTypes.Add(new FilePickerFileType("Executable Files")
            {
                Patterns = new[] { "*.exe", "*.bat", "*.cmd" }
            });
        }
        else
        {
            // Linux/Unix executable file types
            fileTypes.Add(new FilePickerFileType("Executable Files")
            {
                Patterns = new[] { "*" }  // Show all files on Linux since executables often have no extension
            });

            // Common executable patterns for Linux
            fileTypes.Add(new FilePickerFileType("Binary Files")
            {
                Patterns = new[] { "*.bin", "*.elf", "*.out" }
            });
        }

        // All files option for all platforms
        fileTypes.Add(new FilePickerFileType("All Files")
        {
            Patterns = new[] { "*" }  // Use "*" instead of "*.*" to show files without extensions
        });

        return fileTypes;
    }

    private void ClearTerminal(CoreType coreType)
    {
        try
        {
            _processManager.ClearTerminalOutput(coreType);

            // Force immediate UI update on the UI thread
            Avalonia.Threading.Dispatcher.UIThread.Post(() =>
            {
                try
                {
                    // Update specific terminal outputs based on the core type
                    if (coreType == CoreType.All)
                    {
                        this.RaisePropertyChanged(nameof(R5FTerminalOutput));
                        this.RaisePropertyChanged(nameof(A72TerminalOutput));
                        this.RaisePropertyChanged(nameof(C7xTerminalOutput));
                    }
                    else if (coreType == CoreType.R5F)
                    {
                        this.RaisePropertyChanged(nameof(R5FTerminalOutput));
                    }
                    else if (coreType == CoreType.A72)
                    {
                        this.RaisePropertyChanged(nameof(A72TerminalOutput));
                    }
                    else if (coreType == CoreType.C7x)
                    {
                        this.RaisePropertyChanged(nameof(C7xTerminalOutput));
                    }
                }
                catch (Exception uiEx)
                {
                    _logger.LogError("Error updating UI after terminal clear", uiEx);
                }
            });

            StatusMessage = $"Terminal cleared for {coreType.GetDisplayName()}";
            _logger.LogInfo($"Terminal cleared for {coreType}");
        }
        catch (Exception ex)
        {
            StatusMessage = "Error clearing terminal";
            _logger.LogError("Error clearing terminal", ex);
        }
    }

    private void ShowHelp()
    {
        try
        {
            StatusMessage = "Help dialog opened";
            _logger.LogInfo("Help dialog requested");

            var helpDialog = new TDA4_Emulator.Views.HelpDialog();

            // Get the main window to show dialog
            var mainWindow = Avalonia.Application.Current?.ApplicationLifetime is
                Avalonia.Controls.ApplicationLifetimes.IClassicDesktopStyleApplicationLifetime desktop
                ? desktop.MainWindow : null;

            if (mainWindow != null)
            {
                helpDialog.ShowDialog(mainWindow);
            }
            else
            {
                helpDialog.Show();
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error showing help";
            _logger.LogError("Error showing help", ex);
        }
    }



    private void UpdateSourceCoreSelection()
    {
        try
        {
            SelectedSourceCores.Clear();

            if (SourceAllCores)
            {
                SelectedSourceCores.Add(CoreType.All);
                // When "All" is selected, uncheck individual cores
                _sourceR5F = false;
                _sourceA72 = false;
                _sourceC7x = false;
                this.RaisePropertyChanged(nameof(SourceR5F));
                this.RaisePropertyChanged(nameof(SourceA72));
                this.RaisePropertyChanged(nameof(SourceC7x));
            }
            else
            {
                if (SourceR5F) SelectedSourceCores.Add(CoreType.R5F);
                if (SourceA72) SelectedSourceCores.Add(CoreType.A72);
                if (SourceC7x) SelectedSourceCores.Add(CoreType.C7x);

                // If individual cores are selected, uncheck "All"
                if ((SourceR5F || SourceA72 || SourceC7x) && _sourceAllCores)
                {
                    _sourceAllCores = false;
                    this.RaisePropertyChanged(nameof(SourceAllCores));
                }
            }

            _logger.LogInfo($"Source cores updated: {SelectedSourceCores.Count} items: [{string.Join(", ", SelectedSourceCores)}]");
            this.RaisePropertyChanged(nameof(CanSendIpc));
        }
        catch (Exception ex)
        {
            _logger.LogError("Error updating source core selection", ex);
        }
    }

    private void UpdateDestinationCoreSelection()
    {
        try
        {
            SelectedDestinationCores.Clear();

            if (DestAllCores)
            {
                SelectedDestinationCores.Add(CoreType.All);
                // When "All" is selected, uncheck individual cores
                _destR5F = false;
                _destA72 = false;
                _destC7x = false;
                this.RaisePropertyChanged(nameof(DestR5F));
                this.RaisePropertyChanged(nameof(DestA72));
                this.RaisePropertyChanged(nameof(DestC7x));
            }
            else
            {
                if (DestR5F) SelectedDestinationCores.Add(CoreType.R5F);
                if (DestA72) SelectedDestinationCores.Add(CoreType.A72);
                if (DestC7x) SelectedDestinationCores.Add(CoreType.C7x);

                // If individual cores are selected, uncheck "All"
                if ((DestR5F || DestA72 || DestC7x) && _destAllCores)
                {
                    _destAllCores = false;
                    this.RaisePropertyChanged(nameof(DestAllCores));
                }
            }

            _logger.LogInfo($"Destination cores updated: {SelectedDestinationCores.Count} items: [{string.Join(", ", SelectedDestinationCores)}]");
            this.RaisePropertyChanged(nameof(CanSendIpc));
        }
        catch (Exception ex)
        {
            _logger.LogError("Error updating destination core selection", ex);
        }
    }

    /// <summary>
    /// Safely converts a collection of TerminalLine objects to formatted text
    /// </summary>
    private string ConvertTerminalLinesToText(ObservableCollection<TerminalLine>? lines)
    {
        try
        {
            if (lines == null || lines.Count == 0)
            {
                return string.Empty;
            }

            var sb = new StringBuilder();
            foreach (var line in lines)
            {
                if (line != null && !string.IsNullOrEmpty(line.FormattedText))
                {
                    sb.AppendLine(line.FormattedText);
                }
            }
            return sb.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError("Error converting terminal lines to text", ex);
            return string.Empty;
        }
    }

    #endregion

    #region Validation Methods

    private void ValidateR5FPath()
    {
        var oldValid = R5FPathValid;

        if (string.IsNullOrWhiteSpace(R5FBinaryPath))
        {
            R5FPathError = string.Empty;
            this.RaisePropertyChanged(nameof(R5FPathValid));
            _logger.LogInfo($"R5F path validation: Empty path, Valid={R5FPathValid}");
            return;
        }

        if (!File.Exists(R5FBinaryPath))
        {
            R5FPathError = "File does not exist";
            this.RaisePropertyChanged(nameof(R5FPathValid));
            _logger.LogWarning($"R5F path validation: File not found '{R5FBinaryPath}', Valid={R5FPathValid}");
            return;
        }

        if (!IsExecutableFile(R5FBinaryPath))
        {
            R5FPathError = GetExecutableErrorMessage();
            this.RaisePropertyChanged(nameof(R5FPathValid));
            _logger.LogWarning($"R5F path validation: Not executable '{R5FBinaryPath}', Valid={R5FPathValid}");
            return;
        }

        R5FPathError = string.Empty;
        this.RaisePropertyChanged(nameof(R5FPathValid));

        if (oldValid != R5FPathValid)
        {
            _logger.LogInfo($"R5F path validation: '{R5FBinaryPath}' is valid, Valid={R5FPathValid}, CanStartEmulation={CanStartEmulation}");
        }
    }

    private void ValidateA72Path()
    {
        var oldValid = A72PathValid;

        if (string.IsNullOrWhiteSpace(A72BinaryPath))
        {
            A72PathError = string.Empty;
            this.RaisePropertyChanged(nameof(A72PathValid));
            _logger.LogInfo($"A72 path validation: Empty path, Valid={A72PathValid}");
            return;
        }

        if (!File.Exists(A72BinaryPath))
        {
            A72PathError = "File does not exist";
            this.RaisePropertyChanged(nameof(A72PathValid));
            _logger.LogWarning($"A72 path validation: File not found '{A72BinaryPath}', Valid={A72PathValid}");
            return;
        }

        if (!IsExecutableFile(A72BinaryPath))
        {
            A72PathError = GetExecutableErrorMessage();
            this.RaisePropertyChanged(nameof(A72PathValid));
            _logger.LogWarning($"A72 path validation: Not executable '{A72BinaryPath}', Valid={A72PathValid}");
            return;
        }

        A72PathError = string.Empty;
        this.RaisePropertyChanged(nameof(A72PathValid));

        if (oldValid != A72PathValid)
        {
            _logger.LogInfo($"A72 path validation: '{A72BinaryPath}' is valid, Valid={A72PathValid}, CanStartEmulation={CanStartEmulation}");
        }
    }

    private void ValidateC7xPath()
    {
        var oldValid = C7xPathValid;

        if (string.IsNullOrWhiteSpace(C7xBinaryPath))
        {
            C7xPathError = string.Empty;
            this.RaisePropertyChanged(nameof(C7xPathValid));
            _logger.LogInfo($"C7x path validation: Empty path, Valid={C7xPathValid}");
            return;
        }

        if (!File.Exists(C7xBinaryPath))
        {
            C7xPathError = "File does not exist";
            this.RaisePropertyChanged(nameof(C7xPathValid));
            _logger.LogWarning($"C7x path validation: File not found '{C7xBinaryPath}', Valid={C7xPathValid}");
            return;
        }

        if (!IsExecutableFile(C7xBinaryPath))
        {
            C7xPathError = GetExecutableErrorMessage();
            this.RaisePropertyChanged(nameof(C7xPathValid));
            _logger.LogWarning($"C7x path validation: Not executable '{C7xBinaryPath}', Valid={C7xPathValid}");
            return;
        }

        C7xPathError = string.Empty;
        this.RaisePropertyChanged(nameof(C7xPathValid));

        if (oldValid != C7xPathValid)
        {
            _logger.LogInfo($"C7x path validation: '{C7xBinaryPath}' is valid, Valid={C7xPathValid}, CanStartEmulation={CanStartEmulation}");
        }
    }

    private bool IsExecutableFile(string filePath)
    {
        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".exe" || extension == ".bat" || extension == ".cmd";
        }
        else if (Environment.OSVersion.Platform == PlatformID.Unix)
        {
            // On Unix-like systems, check if file has execute permissions
            try
            {
                var fileInfo = new FileInfo(filePath);
                if (!fileInfo.Exists)
                    return false;

                // Check if file is executable by trying to get Unix file permissions
                // This is a more robust check for Linux systems
                var result = System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "test",
                    Arguments = $"-x \"{filePath}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                });

                if (result != null)
                {
                    result.WaitForExit(1000); // Wait max 1 second
                    return result.ExitCode == 0;
                }
            }
            catch
            {
                // Fallback: if we can't check permissions, assume it's executable if it's a regular file
                // This handles cases where the 'test' command might not be available
            }

            // Fallback check: accept files without extension or common executable patterns
            var fileName = Path.GetFileName(filePath).ToLowerInvariant();
            var fileExtension = Path.GetExtension(filePath).ToLowerInvariant();

            // Accept files with no extension (common for Linux executables)
            if (string.IsNullOrEmpty(fileExtension))
                return true;

            // Accept common executable extensions
            return fileExtension == ".bin" || fileExtension == ".elf" || fileExtension == ".out" ||
                   fileName.Contains("_core") || fileName.Contains("tda4");
        }
        else // Unsupported platform
        {
            _logger.LogWarning($"Unsupported platform: {Environment.OSVersion.Platform}");
            return false;
        }
    }

    private string GetExecutableErrorMessage()
    {
        if (Environment.OSVersion.Platform == PlatformID.Win32NT)
        {
            return "File must be .exe, .bat, or .cmd";
        }
        else if (Environment.OSVersion.Platform == PlatformID.Unix)
        {
            return "File must be executable (try: chmod +x filename)";
        } else
        {
            return "Unsupported platform";
        }
    }

    private bool HasValidBinaryPaths()
    {
        return R5FPathValid || A72PathValid || C7xPathValid;
    }

    /// <summary>
    /// Logs the current validation state for debugging
    /// </summary>
    private void LogValidationState(string context)
    {
        _logger.LogInfo($"=== Validation State ({context}) ===");
        _logger.LogInfo($"R5F: Path='{R5FBinaryPath}', Error='{R5FPathError}', Valid={R5FPathValid}");
        _logger.LogInfo($"A72: Path='{A72BinaryPath}', Error='{A72PathError}', Valid={A72PathValid}");
        _logger.LogInfo($"C7x: Path='{C7xBinaryPath}', Error='{C7xPathError}', Valid={C7xPathValid}");
        _logger.LogInfo($"IsEmulationRunning={IsEmulationRunning}");
        _logger.LogInfo($"CanStartEmulation={CanStartEmulation}");
        _logger.LogInfo($"HasValidBinaryPaths={HasValidBinaryPaths()}");
        _logger.LogInfo("=== End Validation State ===");
    }



    /// <summary>
    /// Determines which processes should receive IPC commands based on selected source cores
    /// </summary>
    /// <param name="selectedSourceCores">Collection of selected source cores</param>
    /// <returns>Routing result indicating which processes to target</returns>
    private CommandRoutingResult DetermineCommandRouting(IEnumerable<CoreType> selectedSourceCores)
    {
        var result = new CommandRoutingResult();
        var sourceCores = selectedSourceCores.ToList();

        // Handle "All" selection - expand to individual cores
        if (sourceCores.Contains(CoreType.All))
        {
            sourceCores = CoreTypeExtensions.GetIndividualCores().ToList();
        }

        foreach (var sourceCore in sourceCores)
        {
            switch (sourceCore)
            {
                case CoreType.R5F:
                    result.ShouldSendToQemu = true;
                    result.QemuCores.Add("R5F");
                    break;

                case CoreType.A72:
                    result.ShouldSendToQemu = true;
                    result.QemuCores.Add("A72");
                    break;

                case CoreType.C7x:
                    result.ShouldSendToC7x = true;
                    break;
            }
        }

        _logger.LogInfo($"Command routing determined: QEMU={result.ShouldSendToQemu} (cores: {string.Join(",", result.QemuCores)}), C7x={result.ShouldSendToC7x}");
        return result;
    }

    #endregion

    #region Event Handlers

    private void OnOutputReceived(object? sender, TerminalLine line)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Trigger property change notifications for individual terminal outputs
            this.RaisePropertyChanged(nameof(R5FTerminalOutput));
            this.RaisePropertyChanged(nameof(A72TerminalOutput));
            this.RaisePropertyChanged(nameof(C7xTerminalOutput));
        });
    }

    private void OnProcessStatusChanged(object? sender, ProcessStatusChangedEventArgs e)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            IsEmulationRunning = _processManager.IsAnyProcessRunning;

            if (!e.IsRunning && e.ExitCode.HasValue)
            {
                StatusMessage = $"{e.CoreType.GetDisplayName()} exited with code {e.ExitCode}";
            }
        });
    }

    private void OnQemuOutputReceived(object? sender, TerminalLine line)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Trigger property change notifications for individual terminal outputs
            // This will cause the filtered terminal outputs to refresh
            this.RaisePropertyChanged(nameof(R5FTerminalOutput));
            this.RaisePropertyChanged(nameof(A72TerminalOutput));
            this.RaisePropertyChanged(nameof(C7xTerminalOutput));
        });
    }

    private void OnQemuProcessExited(object? sender, int exitCode)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Check if any processes are still running
            bool anyRunning = _qemuController.IsRunning || _c7xController.IsRunning;
            if (!anyRunning)
            {
                IsEmulationRunning = false;
            }

            StatusMessage = $"QEMU process exited with code {exitCode}";
            _logger.LogInfo($"QEMU process exited with code {exitCode}");
        });
    }

    private void OnC7xOutputReceived(object? sender, TerminalLine line)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Trigger property change notification for C7x terminal output
            this.RaisePropertyChanged(nameof(C7xTerminalOutput));
        });
    }

    private void OnC7xProcessExited(object? sender, int exitCode)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Check if any processes are still running
            bool anyRunning = _qemuController.IsRunning || _c7xController.IsRunning;
            if (!anyRunning)
            {
                IsEmulationRunning = false;
            }

            StatusMessage = $"C7x process exited with code {exitCode}";
            _logger.LogInfo($"C7x process exited with code {exitCode}");
        });
    }

    #endregion

    #region Configuration Management

    /// <summary>
    /// Loads configuration from file and updates UI
    /// </summary>
    private async Task LoadConfigurationAsync()
    {
        try
        {
            var config = await _configurationService.LoadConfigurationAsync();

            // Update UI on main thread
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                // Update binary paths without triggering save
                _r5fBinaryPath = config.R5FBinaryPaths;
                _a72BinaryPath = config.A72BinaryPaths;
                _c7xBinaryPath = config.C7xBinaryPaths;

                // Raise property changed events
                this.RaisePropertyChanged(nameof(R5FBinaryPath));
                this.RaisePropertyChanged(nameof(A72BinaryPath));
                this.RaisePropertyChanged(nameof(C7xBinaryPath));

                // Trigger validation
                ValidateR5FPath();
                ValidateA72Path();
                ValidateC7xPath();

                StatusMessage = "Configuration loaded successfully";
                _logger.LogInfo("Configuration loaded and UI updated");
            });
        }
        catch (Exception ex)
        {
            await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                StatusMessage = "Error loading configuration";
            });
            _logger.LogError("Error loading configuration in ViewModel", ex);
        }
    }

    /// <summary>
    /// Resets configuration to default values
    /// </summary>
    private async Task ResetConfigurationAsync()
    {
        try
        {
            StatusMessage = "Resetting configuration...";
            _logger.LogInfo("Resetting configuration to defaults");

            var success = await _configurationService.ResetToDefaultAsync();

            if (success)
            {
                // Reload configuration to update UI
                await LoadConfigurationAsync();
                StatusMessage = "Configuration reset to defaults";
                _logger.LogInfo("Configuration reset successfully");
            }
            else
            {
                StatusMessage = "Failed to reset configuration";
                _logger.LogError("Failed to reset configuration");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error resetting configuration";
            _logger.LogError("Error resetting configuration", ex);
        }
    }

    /// <summary>
    /// Manually saves current configuration
    /// </summary>
    private async Task SaveConfigurationAsync()
    {
        try
        {
            StatusMessage = "Saving configuration...";
            _logger.LogInfo("Manually saving configuration");

            var config = await _configurationService.GetConfigurationAsync();

            // Update config with current values
            config.R5FBinaryPaths = R5FBinaryPath;
            config.A72BinaryPaths = A72BinaryPath;
            config.C7xBinaryPaths = C7xBinaryPath;

            var success = await _configurationService.SaveConfigurationAsync(config);

            if (success)
            {
                StatusMessage = "Configuration saved successfully";
                _logger.LogInfo("Configuration saved manually");
            }
            else
            {
                StatusMessage = "Failed to save configuration";
                _logger.LogError("Failed to save configuration manually");
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error saving configuration";
            _logger.LogError("Error saving configuration manually", ex);
        }
    }

    #endregion

    #region Application Shutdown

    /// <summary>
    /// Performs graceful shutdown of all running processes and resources
    /// </summary>
    public async Task ShutdownAsync()
    {
        try
        {
            _logger.LogInfo("Starting application shutdown sequence");

            // Stop emulation if it's running
            if (IsEmulationRunning)
            {
                _logger.LogInfo("Stopping emulation processes during shutdown");

                // Use a timeout to prevent hanging during shutdown
                var shutdownTask = StopEmulationAsync();
                var timeoutTask = Task.Delay(TimeSpan.FromSeconds(15));

                var completedTask = await Task.WhenAny(shutdownTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    _logger.LogWarning("Emulation shutdown timed out after 15 seconds");
                    StatusMessage = "Shutdown timed out - forcing process termination";
                }
                else
                {
                    _logger.LogInfo("Emulation stopped successfully during shutdown");
                }
            }

            // Give processes a moment to fully terminate
            await Task.Delay(1000);

            // Ensure all process managers are properly disposed
            try
            {
                _processManager?.Dispose();
                _logger.LogInfo("ProcessManager disposed");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error disposing ProcessManager: {ex.Message}");
            }

            try
            {
                _qemuController?.Dispose();
                _logger.LogInfo("QemuController disposed");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error disposing QemuController: {ex.Message}");
            }

            try
            {
                _c7xController?.Dispose();
                _logger.LogInfo("C7xController disposed");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error disposing C7xController: {ex.Message}");
            }

            _logger.LogInfo("Application shutdown completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError("Error during application shutdown", ex);
            throw;
        }
    }

    #endregion

    #region IDisposable

    public override void Dispose()
    {
        _processManager?.Dispose();
        _qemuController?.Dispose();
        _c7xController?.Dispose();
        base.Dispose();
    }

    #endregion
}

/// <summary>
/// Result of command routing analysis for IPC messages
/// </summary>
internal class CommandRoutingResult
{
    /// <summary>
    /// Whether the command should be sent to the QEMU process
    /// </summary>
    public bool ShouldSendToQemu { get; set; }

    /// <summary>
    /// Whether the command should be sent to the C7x process
    /// </summary>
    public bool ShouldSendToC7x { get; set; }

    /// <summary>
    /// List of QEMU cores that should receive the command
    /// </summary>
    public List<string> QemuCores { get; set; } = new();
}