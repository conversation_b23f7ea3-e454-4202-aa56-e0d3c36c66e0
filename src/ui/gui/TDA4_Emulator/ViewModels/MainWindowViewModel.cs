﻿using System;
using System.Reactive;
using System.Threading.Tasks;
using ReactiveUI;
using TDA4_Emulator.Models;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.ViewModels;

/// <summary>
/// Main window view model that acts as the wizard container
/// </summary>
public class MainWindowViewModel : ViewModelBase
{
    private readonly LoggingService _logger;
    private readonly WizardNavigationService _navigationService;
    private readonly ConfigurationService _configurationService;
    private readonly ProcessManager _processManager;

    // Step ViewModels
    private readonly ModelSelectionViewModel _modelSelectionViewModel;
    private readonly ConfigurationViewModel _configurationViewModel;
    private readonly EmulationViewModel _emulationViewModel;

    // UI state properties
    private WizardStep _currentStep = WizardStep.ModelSelection;
    private string _statusMessage = "Ready";
    private ViewModelBase? _currentStepViewModel;



    public MainWindowViewModel()
    {
        _logger = LoggingService.Instance;
        _configurationService = new ConfigurationService(_logger);
        _processManager = new ProcessManager();
        _navigationService = new WizardNavigationService(_logger);

        // Initialize step view models
        _modelSelectionViewModel = new ModelSelectionViewModel(_logger, _navigationService);
        _configurationViewModel = new ConfigurationViewModel(_logger, _navigationService, _configurationService);
        _emulationViewModel = new EmulationViewModel(_logger, _navigationService, _processManager);

        // Initialize commands
        ShowHelpCommand = ReactiveCommand.Create(ShowHelp);

        // Subscribe to navigation service events
        _navigationService.CurrentStepChanged += OnCurrentStepChanged;

        // Set initial step
        UpdateCurrentStep();

        _logger.LogInfo("TDA4 Emulator Control Panel (Wizard) initialized");
    }

    #region Properties

    /// <summary>
    /// Current wizard step
    /// </summary>
    public WizardStep CurrentStep
    {
        get => _currentStep;
        private set => this.RaiseAndSetIfChanged(ref _currentStep, value);
    }

    /// <summary>
    /// Current step view model
    /// </summary>
    public ViewModelBase? CurrentStepViewModel
    {
        get => _currentStepViewModel;
        private set => this.RaiseAndSetIfChanged(ref _currentStepViewModel, value);
    }

    /// <summary>
    /// Current status message
    /// </summary>
    public string StatusMessage
    {
        get => _statusMessage;
        set => this.RaiseAndSetIfChanged(ref _statusMessage, value);
    }

    /// <summary>
    /// Model selection view model
    /// </summary>
    public ModelSelectionViewModel ModelSelectionViewModel => _modelSelectionViewModel;

    /// <summary>
    /// Configuration view model
    /// </summary>
    public ConfigurationViewModel ConfigurationViewModel => _configurationViewModel;

    /// <summary>
    /// Emulation view model
    /// </summary>
    public EmulationViewModel EmulationViewModel => _emulationViewModel;

    /// <summary>
    /// Navigation service
    /// </summary>
    public WizardNavigationService NavigationService => _navigationService;

    /// <summary>
    /// Indicates if the current step is Step 1 (Model Selection)
    /// </summary>
    public bool IsStep1 => CurrentStep == WizardStep.ModelSelection;

    /// <summary>
    /// Indicates if the current step is Step 2 (Configuration)
    /// </summary>
    public bool IsStep2 => CurrentStep == WizardStep.Configuration;

    /// <summary>
    /// Indicates if the current step is Step 3 (Emulation)
    /// </summary>
    public bool IsStep3 => CurrentStep == WizardStep.Emulation;

    #endregion

    #region Commands

    /// <summary>
    /// Command to show help dialog
    /// </summary>
    public ReactiveCommand<Unit, Unit> ShowHelpCommand { get; }

    #endregion

    #region Private Methods

    /// <summary>
    /// Handles wizard step changes
    /// </summary>
    private void OnCurrentStepChanged(object? sender, WizardStepChangedEventArgs e)
    {
        CurrentStep = e.NewStep;
        UpdateCurrentStep();
        UpdateStatusMessage();
    }

    /// <summary>
    /// Updates the current step view model
    /// </summary>
    private void UpdateCurrentStep()
    {
        CurrentStepViewModel = CurrentStep switch
        {
            WizardStep.ModelSelection => _modelSelectionViewModel,
            WizardStep.Configuration => _configurationViewModel,
            WizardStep.Emulation => _emulationViewModel,
            _ => null
        };

        // Update step indicators
        this.RaisePropertyChanged(nameof(IsStep1));
        this.RaisePropertyChanged(nameof(IsStep2));
        this.RaisePropertyChanged(nameof(IsStep3));

        _logger.LogInfo($"Updated current step to: {CurrentStep}");
    }

    /// <summary>
    /// Updates the status message based on current step
    /// </summary>
    private void UpdateStatusMessage()
    {
        StatusMessage = CurrentStep switch
        {
            WizardStep.ModelSelection => "Select TDA4 model and test mode",
            WizardStep.Configuration => "Configure core binary paths and settings",
            WizardStep.Emulation => "Control emulation and inter-processor communication",
            _ => "Ready"
        };
    }

    /// <summary>
    /// Shows the help dialog
    /// </summary>
    private void ShowHelp()
    {
        try
        {
            StatusMessage = "Help dialog opened";
            _logger.LogInfo("Help dialog requested");

            var helpDialog = new TDA4_Emulator.Views.HelpDialog();

            // Get the main window to show dialog
            var mainWindow = Avalonia.Application.Current?.ApplicationLifetime is
                Avalonia.Controls.ApplicationLifetimes.IClassicDesktopStyleApplicationLifetime desktop
                ? desktop.MainWindow : null;

            if (mainWindow != null)
            {
                helpDialog.ShowDialog(mainWindow);
            }
            else
            {
                helpDialog.Show();
            }
        }
        catch (Exception ex)
        {
            StatusMessage = "Error showing help";
            _logger.LogError("Error showing help", ex);
        }
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// Gets the current configuration from all steps
    /// </summary>
    public ConfigurationSettings GetCurrentConfiguration()
    {
        var config = _configurationViewModel.GetConfiguration();
        var (model, testMode) = _modelSelectionViewModel.GetConfiguration();

        config.SelectedModel = model;
        config.SelectedTestMode = testMode;

        return config;
    }

    /// <summary>
    /// Resets the wizard to the beginning
    /// </summary>
    public void ResetWizard()
    {
        _logger.LogInfo("Resetting wizard to beginning");

        _navigationService.Reset();
        _modelSelectionViewModel.Reset();

        StatusMessage = "Wizard reset to beginning";
    }

    /// <summary>
    /// Performs graceful shutdown of the application
    /// </summary>
    public async Task ShutdownAsync()
    {
        try
        {
            _logger.LogInfo("Performing graceful shutdown");

            // Stop any running emulation processes
            if (_emulationViewModel.IsEmulationRunning)
            {
                await _emulationViewModel.StopEmulationAsync();
            }

            // Save configuration if auto-save is enabled
            if (_configurationViewModel.AutoSaveConfiguration)
            {
                await _configurationViewModel.SaveConfigurationAsync();
            }

            _logger.LogInfo("Graceful shutdown completed");
        }
        catch (Exception ex)
        {
            _logger.LogError("Error during graceful shutdown", ex);
        }
    }

    #endregion
}
