using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using ReactiveUI;
using TDA4_Emulator.Models;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.ViewModels;

/// <summary>
/// ViewModel for Step 1: Model Selection
/// </summary>
public class ModelSelectionViewModel : ViewModelBase
{
    private readonly LoggingService _logger;
    private readonly WizardNavigationService _navigationService;
    
    private TDA4Model _selectedModel = TDA4Model.TDA4VM;
    private TestMode _selectedTestMode = TestMode.IPC;

    public ModelSelectionViewModel(LoggingService logger, WizardNavigationService navigationService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));

        // Initialize collections
        AvailableModels = new ObservableCollection<TDA4Model>(TDA4ModelExtensions.GetAllModels());
        AvailableTestModes = new ObservableCollection<TestMode>();

        // Initialize commands
        NextCommand = ReactiveCommand.Create(NavigateNext, this.WhenAnyValue(x => x.CanNavigateNext));

        // Set up property change handlers
        this.WhenAnyValue(x => x.SelectedModel)
            .Subscribe(OnSelectedModelChanged);

        this.WhenAnyValue(x => x.SelectedTestMode)
            .Subscribe(OnSelectedTestModeChanged);

        // Initialize available test modes based on default model
        UpdateAvailableTestModes();
        
        // Validate initial state
        ValidateStep();

        _logger.LogInfo("ModelSelectionViewModel initialized");
    }

    #region Properties

    /// <summary>
    /// Available TDA4 models for selection
    /// </summary>
    public ObservableCollection<TDA4Model> AvailableModels { get; }

    /// <summary>
    /// Available test modes for the selected model
    /// </summary>
    public ObservableCollection<TestMode> AvailableTestModes { get; }

    /// <summary>
    /// Currently selected TDA4 model
    /// </summary>
    public TDA4Model SelectedModel
    {
        get => _selectedModel;
        set => this.RaiseAndSetIfChanged(ref _selectedModel, value);
    }

    /// <summary>
    /// Currently selected test mode
    /// </summary>
    public TestMode SelectedTestMode
    {
        get => _selectedTestMode;
        set => this.RaiseAndSetIfChanged(ref _selectedTestMode, value);
    }

    /// <summary>
    /// Description of the selected model
    /// </summary>
    public string SelectedModelDescription => SelectedModel.GetDescription();

    /// <summary>
    /// Description of the selected test mode
    /// </summary>
    public string SelectedTestModeDescription => SelectedTestMode.GetDescription();

    /// <summary>
    /// Determines if the step is valid and can navigate to next
    /// </summary>
    public bool CanNavigateNext => IsStepValid();

    #endregion

    #region Commands

    /// <summary>
    /// Command to navigate to the next step
    /// </summary>
    public ReactiveCommand<Unit, Unit> NextCommand { get; }

    #endregion

    #region Private Methods

    /// <summary>
    /// Handles changes to the selected model
    /// </summary>
    private void OnSelectedModelChanged(TDA4Model model)
    {
        _logger.LogInfo($"Selected model changed to: {model.GetDisplayName()}");
        
        // Update available test modes based on the selected model
        UpdateAvailableTestModes();
        
        // Ensure the selected test mode is supported by the new model
        if (!model.SupportsTestMode(SelectedTestMode))
        {
            var supportedModes = model.GetSupportedTestModes().ToList();
            if (supportedModes.Any())
            {
                SelectedTestMode = supportedModes.First();
                _logger.LogInfo($"Test mode changed to {SelectedTestMode.GetDisplayName()} (first supported mode for {model.GetDisplayName()})");
            }
        }

        // Update property notifications
        this.RaisePropertyChanged(nameof(SelectedModelDescription));
        
        // Validate the step
        ValidateStep();
    }

    /// <summary>
    /// Handles changes to the selected test mode
    /// </summary>
    private void OnSelectedTestModeChanged(TestMode testMode)
    {
        _logger.LogInfo($"Selected test mode changed to: {testMode.GetDisplayName()}");
        
        // Update property notifications
        this.RaisePropertyChanged(nameof(SelectedTestModeDescription));
        
        // Validate the step
        ValidateStep();
    }

    /// <summary>
    /// Updates the available test modes based on the selected model
    /// </summary>
    private void UpdateAvailableTestModes()
    {
        var supportedModes = SelectedModel.GetSupportedTestModes().ToList();
        
        AvailableTestModes.Clear();
        foreach (var mode in supportedModes)
        {
            AvailableTestModes.Add(mode);
        }

        _logger.LogInfo($"Updated available test modes for {SelectedModel.GetDisplayName()}: {AvailableTestModes.Count} modes");
    }

    /// <summary>
    /// Validates the current step
    /// </summary>
    private void ValidateStep()
    {
        var isValid = IsStepValid();
        _navigationService.SetStepValidation(WizardStep.ModelSelection, isValid);
        
        // Update UI
        this.RaisePropertyChanged(nameof(CanNavigateNext));
        
        _logger.LogInfo($"Model selection step validation: {isValid}");
    }

    /// <summary>
    /// Determines if the step is valid
    /// </summary>
    private bool IsStepValid()
    {
        // Step is valid if both model and test mode are selected and compatible
        return SelectedModel.SupportsTestMode(SelectedTestMode);
    }

    /// <summary>
    /// Navigates to the next step
    /// </summary>
    private void NavigateNext()
    {
        if (!CanNavigateNext)
        {
            _logger.LogWarning("Cannot navigate to next step - validation failed");
            return;
        }

        _logger.LogInfo($"Navigating to next step with Model={SelectedModel.GetDisplayName()}, TestMode={SelectedTestMode.GetDisplayName()}");
        
        if (!_navigationService.NavigateNext())
        {
            _logger.LogError("Failed to navigate to next step");
        }
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// Gets the current configuration for this step
    /// </summary>
    public (TDA4Model Model, TestMode TestMode) GetConfiguration()
    {
        return (SelectedModel, SelectedTestMode);
    }

    /// <summary>
    /// Sets the configuration for this step
    /// </summary>
    public void SetConfiguration(TDA4Model model, TestMode testMode)
    {
        _logger.LogInfo($"Setting configuration: Model={model.GetDisplayName()}, TestMode={testMode.GetDisplayName()}");
        
        SelectedModel = model;
        SelectedTestMode = testMode;
    }

    /// <summary>
    /// Resets the step to default values
    /// </summary>
    public void Reset()
    {
        _logger.LogInfo("Resetting model selection to defaults");
        
        SelectedModel = TDA4Model.TDA4VM;
        SelectedTestMode = TestMode.IPC;
    }

    #endregion
}
