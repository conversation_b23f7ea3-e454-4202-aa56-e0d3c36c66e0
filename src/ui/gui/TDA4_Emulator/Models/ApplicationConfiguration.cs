using System.Text.Json.Serialization;

namespace TDA4_Emulator.Models;

/// <summary>
/// Configuration model for TDA4 Emulator application settings
/// </summary>
public class ApplicationConfiguration
{
    /// <summary>
    /// QEMU binary paths for different core types
    /// </summary>
    [JsonPropertyName("qemuBinaryPaths")]
    public QemuBinaryPaths QemuBinaryPaths { get; set; } = new();

    /// <summary>
    /// R5F core binary paths
    /// </summary>
    [JsonPropertyName("r5FBinaryPaths")]
    public string R5FBinaryPaths { get; set; } = string.Empty;

    /// <summary>
    /// A72 core binary paths
    /// </summary>
    [JsonPropertyName("a72BinaryPaths")]
    public string A72BinaryPaths { get; set; } = string.Empty;

    /// <summary>
    /// C7x core binary paths
    /// </summary>
    [JsonPropertyName("c7xBinaryPaths")]
    public string C7xBinaryPaths { get; set; } = string.Empty;

    /// <summary>
    /// Application settings
    /// </summary>
    [JsonPropertyName("settings")]
    public ApplicationSettings Settings { get; set; } = new();

    /// <summary>
    /// Converts the configuration to a string for logging
    /// </summary>
    public string toString()
    {
        return $"QEMU: {QemuBinaryPaths.GetPath()}\nR5F: {R5FBinaryPaths}\nA72: {A72BinaryPaths}\nC7x: {C7xBinaryPaths}\n" + Settings.toString();
    }
}

/// <summary>
/// QEMU binary paths configuration
/// </summary>
public class QemuBinaryPaths
{
    /// <summary>
    /// Path to QEMU system binary used for both R5F and A72 cores
    /// </summary>
    [JsonPropertyName("qemuBinary")]
    public string QemuBinary { get; set; } = string.Empty;

    /// <summary>
    /// Gets the QEMU binary path (same for both R5F and A72 cores)
    /// </summary>
    public string GetPath()
    {
        return QemuBinary;
    }

    /// <summary>
    /// Sets the QEMU binary path
    /// </summary>
    public void SetPath(string path)
    {
        QemuBinary = path ?? string.Empty;
    }
}

/// <summary>
/// Application settings
/// </summary>
public class ApplicationSettings
{
    /// <summary>
    /// Whether to auto-save configuration on changes
    /// </summary>
    [JsonPropertyName("autoSaveConfiguration")]
    public bool AutoSaveConfiguration { get; set; } = true;

    /// <summary>
    /// Whether to validate binary paths on startup
    /// </summary>
    [JsonPropertyName("validateBinaryPathsOnStartup")]
    public bool ValidateBinaryPathsOnStartup { get; set; } = true;

    /// <summary>
    /// Default timeout for process operations in seconds
    /// </summary>
    [JsonPropertyName("processTimeoutSeconds")]
    public int ProcessTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Converts the settings to a string for logging
    /// </summary>
    public string toString()
    {
        return $"Auto-save: {AutoSaveConfiguration}\nValidate on startup: {ValidateBinaryPathsOnStartup}\nProcess timeout: {ProcessTimeoutSeconds}";
    }
}
