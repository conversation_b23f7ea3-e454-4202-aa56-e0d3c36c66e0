using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace TDA4_Emulator.Models;

/// <summary>
/// Comprehensive configuration settings for the TDA4 Emulator
/// </summary>
public class ConfigurationSettings
{
    /// <summary>
    /// Selected TDA4 model variant
    /// </summary>
    public TDA4Model SelectedModel { get; set; } = TDA4Model.TDA4VM;

    /// <summary>
    /// Selected test mode
    /// </summary>
    public TestMode SelectedTestMode { get; set; } = TestMode.IPC;

    /// <summary>
    /// Binary paths for all 14 cores
    /// </summary>
    public CoreBinaryPaths BinaryPaths { get; set; } = new();

    /// <summary>
    /// QEMU binary path configuration
    /// </summary>
    public string QemuBinaryPath { get; set; } = string.Empty;

    /// <summary>
    /// Auto-save configuration setting
    /// </summary>
    public bool AutoSaveConfiguration { get; set; } = true;

    /// <summary>
    /// Process timeout in seconds
    /// </summary>
    [Range(1, 3600)]
    public int ProcessTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Last modified timestamp
    /// </summary>
    public DateTime LastModified { get; set; } = DateTime.Now;

    /// <summary>
    /// Configuration version for compatibility
    /// </summary>
    public string Version { get; set; } = "1.0";
}

/// <summary>
/// Binary paths for all TDA4 cores organized by groups
/// </summary>
public class CoreBinaryPaths
{
    /// <summary>
    /// ARM A72 core binary paths
    /// </summary>
    public A72BinaryPaths A72Paths { get; set; } = new();

    /// <summary>
    /// ARM MCU R5F core binary paths
    /// </summary>
    public McuR5FBinaryPaths McuR5FPaths { get; set; } = new();

    /// <summary>
    /// ARM Main R5F core binary paths
    /// </summary>
    public MainR5FBinaryPaths MainR5FPaths { get; set; } = new();

    /// <summary>
    /// DSP C7x core binary paths
    /// </summary>
    public C7xBinaryPaths C7xPaths { get; set; } = new();

    /// <summary>
    /// Gets the binary path for a specific core type
    /// </summary>
    public string GetPath(CoreType coreType)
    {
        return coreType switch
        {
            CoreType.A72_VM0 => A72Paths.A72_VM0,
            CoreType.A72_VM1 => A72Paths.A72_VM1,
            CoreType.MCU_R5F_0 => McuR5FPaths.MCU_R5F_0,
            CoreType.MCU_R5F_1 => McuR5FPaths.MCU_R5F_1,
            CoreType.MCU_R5F_2 => McuR5FPaths.MCU_R5F_2,
            CoreType.MCU_R5F_3 => McuR5FPaths.MCU_R5F_3,
            CoreType.MCU_R5F_4 => McuR5FPaths.MCU_R5F_4,
            CoreType.MCU_R5F_5 => McuR5FPaths.MCU_R5F_5,
            CoreType.Main_R5F_0 => MainR5FPaths.Main_R5F_0,
            CoreType.Main_R5F_1 => MainR5FPaths.Main_R5F_1,
            CoreType.Main_R5F_2 => MainR5FPaths.Main_R5F_2,
            CoreType.Main_R5F_3 => MainR5FPaths.Main_R5F_3,
            CoreType.Main_R5F_4 => MainR5FPaths.Main_R5F_4,
            CoreType.Main_R5F_5 => MainR5FPaths.Main_R5F_5,
            CoreType.C7x_0 => C7xPaths.C7x_0,
            CoreType.C7x_1 => C7xPaths.C7x_1,
            CoreType.C7x_2 => C7xPaths.C7x_2,
            CoreType.C7x_3 => C7xPaths.C7x_3,
            // Legacy support
            CoreType.A72 => A72Paths.A72_VM0,
            CoreType.R5F => McuR5FPaths.MCU_R5F_0,
            CoreType.C7x => C7xPaths.C7x_0,
            _ => string.Empty
        };
    }

    /// <summary>
    /// Sets the binary path for a specific core type
    /// </summary>
    public void SetPath(CoreType coreType, string path)
    {
        switch (coreType)
        {
            case CoreType.A72_VM0: A72Paths.A72_VM0 = path; break;
            case CoreType.A72_VM1: A72Paths.A72_VM1 = path; break;
            case CoreType.MCU_R5F_0: McuR5FPaths.MCU_R5F_0 = path; break;
            case CoreType.MCU_R5F_1: McuR5FPaths.MCU_R5F_1 = path; break;
            case CoreType.MCU_R5F_2: McuR5FPaths.MCU_R5F_2 = path; break;
            case CoreType.MCU_R5F_3: McuR5FPaths.MCU_R5F_3 = path; break;
            case CoreType.MCU_R5F_4: McuR5FPaths.MCU_R5F_4 = path; break;
            case CoreType.MCU_R5F_5: McuR5FPaths.MCU_R5F_5 = path; break;
            case CoreType.Main_R5F_0: MainR5FPaths.Main_R5F_0 = path; break;
            case CoreType.Main_R5F_1: MainR5FPaths.Main_R5F_1 = path; break;
            case CoreType.Main_R5F_2: MainR5FPaths.Main_R5F_2 = path; break;
            case CoreType.Main_R5F_3: MainR5FPaths.Main_R5F_3 = path; break;
            case CoreType.Main_R5F_4: MainR5FPaths.Main_R5F_4 = path; break;
            case CoreType.Main_R5F_5: MainR5FPaths.Main_R5F_5 = path; break;
            case CoreType.C7x_0: C7xPaths.C7x_0 = path; break;
            case CoreType.C7x_1: C7xPaths.C7x_1 = path; break;
            case CoreType.C7x_2: C7xPaths.C7x_2 = path; break;
            case CoreType.C7x_3: C7xPaths.C7x_3 = path; break;
            // Legacy support
            case CoreType.A72: A72Paths.A72_VM0 = path; break;
            case CoreType.R5F: McuR5FPaths.MCU_R5F_0 = path; break;
            case CoreType.C7x: C7xPaths.C7x_0 = path; break;
        }
    }

    /// <summary>
    /// Gets all configured binary paths
    /// </summary>
    public Dictionary<CoreType, string> GetAllPaths()
    {
        var paths = new Dictionary<CoreType, string>();
        
        foreach (var coreType in CoreTypeExtensions.GetIndividualCores())
        {
            var path = GetPath(coreType);
            if (!string.IsNullOrWhiteSpace(path))
            {
                paths[coreType] = path;
            }
        }
        
        return paths;
    }

    /// <summary>
    /// Clears all binary paths
    /// </summary>
    public void ClearAllPaths()
    {
        A72Paths = new A72BinaryPaths();
        McuR5FPaths = new McuR5FBinaryPaths();
        MainR5FPaths = new MainR5FBinaryPaths();
        C7xPaths = new C7xBinaryPaths();
    }
}

/// <summary>
/// ARM A72 core binary paths
/// </summary>
public class A72BinaryPaths
{
    public string A72_VM0 { get; set; } = string.Empty;
    public string A72_VM1 { get; set; } = string.Empty;
}

/// <summary>
/// ARM MCU R5F core binary paths
/// </summary>
public class McuR5FBinaryPaths
{
    public string MCU_R5F_0 { get; set; } = string.Empty;
    public string MCU_R5F_1 { get; set; } = string.Empty;
    public string MCU_R5F_2 { get; set; } = string.Empty;
    public string MCU_R5F_3 { get; set; } = string.Empty;
    public string MCU_R5F_4 { get; set; } = string.Empty;
    public string MCU_R5F_5 { get; set; } = string.Empty;
}

/// <summary>
/// ARM Main R5F core binary paths
/// </summary>
public class MainR5FBinaryPaths
{
    public string Main_R5F_0 { get; set; } = string.Empty;
    public string Main_R5F_1 { get; set; } = string.Empty;
    public string Main_R5F_2 { get; set; } = string.Empty;
    public string Main_R5F_3 { get; set; } = string.Empty;
    public string Main_R5F_4 { get; set; } = string.Empty;
    public string Main_R5F_5 { get; set; } = string.Empty;
}

/// <summary>
/// DSP C7x core binary paths
/// </summary>
public class C7xBinaryPaths
{
    public string C7x_0 { get; set; } = string.Empty;
    public string C7x_1 { get; set; } = string.Empty;
    public string C7x_2 { get; set; } = string.Empty;
    public string C7x_3 { get; set; } = string.Empty;
}
