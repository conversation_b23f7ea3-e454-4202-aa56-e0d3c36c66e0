using System.Collections.Generic;

namespace TDA4_Emulator.Models;

/// <summary>
/// Represents the different steps in the wizard workflow
/// </summary>
public enum WizardStep
{
    /// <summary>
    /// Step 1: Model and test mode selection
    /// </summary>
    ModelSelection,

    /// <summary>
    /// Step 2: Core configuration and settings
    /// </summary>
    Configuration,

    /// <summary>
    /// Step 3: Emulation control and IPC communication
    /// </summary>
    Emulation
}

/// <summary>
/// Extension methods for WizardStep enum
/// </summary>
public static class WizardStepExtensions
{
    /// <summary>
    /// Gets the display name for the wizard step
    /// </summary>
    public static string GetDisplayName(this WizardStep step)
    {
        return step switch
        {
            WizardStep.ModelSelection => "Model Selection",
            WizardStep.Configuration => "Configuration",
            WizardStep.Emulation => "Emulation",
            _ => step.ToString()
        };
    }

    /// <summary>
    /// Gets the step number (1-based)
    /// </summary>
    public static int GetStepNumber(this WizardStep step)
    {
        return step switch
        {
            WizardStep.ModelSelection => 1,
            WizardStep.Configuration => 2,
            WizardStep.Emulation => 3,
            _ => 0
        };
    }

    /// <summary>
    /// Gets the description for the wizard step
    /// </summary>
    public static string GetDescription(this WizardStep step)
    {
        return step switch
        {
            WizardStep.ModelSelection => "Select TDA4 model and test mode",
            WizardStep.Configuration => "Configure core binary paths and settings",
            WizardStep.Emulation => "Control emulation and inter-processor communication",
            _ => "Unknown step"
        };
    }

    /// <summary>
    /// Gets all wizard steps in order
    /// </summary>
    public static IEnumerable<WizardStep> GetAllSteps()
    {
        return new[] { WizardStep.ModelSelection, WizardStep.Configuration, WizardStep.Emulation };
    }

    /// <summary>
    /// Gets the next step in the wizard
    /// </summary>
    public static WizardStep? GetNextStep(this WizardStep currentStep)
    {
        return currentStep switch
        {
            WizardStep.ModelSelection => WizardStep.Configuration,
            WizardStep.Configuration => WizardStep.Emulation,
            WizardStep.Emulation => null,
            _ => null
        };
    }

    /// <summary>
    /// Gets the previous step in the wizard
    /// </summary>
    public static WizardStep? GetPreviousStep(this WizardStep currentStep)
    {
        return currentStep switch
        {
            WizardStep.ModelSelection => null,
            WizardStep.Configuration => WizardStep.ModelSelection,
            WizardStep.Emulation => WizardStep.Configuration,
            _ => null
        };
    }

    /// <summary>
    /// Determines if the step can navigate to the next step
    /// </summary>
    public static bool CanNavigateNext(this WizardStep currentStep)
    {
        return currentStep.GetNextStep() != null;
    }

    /// <summary>
    /// Determines if the step can navigate to the previous step
    /// </summary>
    public static bool CanNavigatePrevious(this WizardStep currentStep)
    {
        return currentStep.GetPreviousStep() != null;
    }
}
