using System.Collections.Generic;
using System.Linq;

namespace TDA4_Emulator.Models;

/// <summary>
/// Represents the different TDA4 model variants available in the emulator
/// </summary>
public enum TDA4Model
{
    /// <summary>
    /// TDA4VM - TDA4 Virtual Machine model
    /// </summary>
    TDA4VM,

    /// <summary>
    /// Dummy model 1 - placeholder for future TDA4 model variant
    /// </summary>
    DummyModel1,

    /// <summary>
    /// Dummy model 2 - placeholder for future TDA4 model variant
    /// </summary>
    DummyModel2
}

/// <summary>
/// Extension methods for TDA4Model enum
/// </summary>
public static class TDA4ModelExtensions
{
    /// <summary>
    /// Gets the display name for the TDA4 model
    /// </summary>
    public static string GetDisplayName(this TDA4Model model)
    {
        return model switch
        {
            TDA4Model.TDA4VM => "TDA4VM",
            TDA4Model.DummyModel1 => "Dummy model 1",
            TDA4Model.DummyModel2 => "Dummy model 2",
            _ => model.ToString()
        };
    }

    /// <summary>
    /// Gets the description for the TDA4 model
    /// </summary>
    public static string GetDescription(this TDA4Model model)
    {
        return model switch
        {
            TDA4Model.TDA4VM => "TDA4 Virtual Machine - Full featured TDA4 emulation",
            TDA4Model.DummyModel1 => "Dummy model 1 - Placeholder for future TDA4 variant",
            TDA4Model.DummyModel2 => "Dummy model 2 - Placeholder for future TDA4 variant",
            _ => "Unknown TDA4 model"
        };
    }

    /// <summary>
    /// Gets all available TDA4 models
    /// </summary>
    public static IEnumerable<TDA4Model> GetAllModels()
    {
        return new[] { TDA4Model.TDA4VM, TDA4Model.DummyModel1, TDA4Model.DummyModel2 };
    }

    /// <summary>
    /// Gets the supported core types for the TDA4 model
    /// </summary>
    public static IEnumerable<CoreType> GetSupportedCores(this TDA4Model model)
    {
        return model switch
        {
            TDA4Model.TDA4VM => CoreTypeExtensions.GetIndividualCores(),
            TDA4Model.DummyModel1 => CoreTypeExtensions.GetIndividualCores(),
            TDA4Model.DummyModel2 => CoreTypeExtensions.GetIndividualCores(),
            _ => new CoreType[0]
        };
    }

    /// <summary>
    /// Gets the supported test modes for the TDA4 model
    /// </summary>
    public static IEnumerable<TestMode> GetSupportedTestModes(this TDA4Model model)
    {
        return model switch
        {
            TDA4Model.TDA4VM => TestModeExtensions.GetAllTestModes(),
            TDA4Model.DummyModel1 => new[] { TestMode.IPC },
            TDA4Model.DummyModel2 => new[] { TestMode.IPC },
            _ => new TestMode[0]
        };
    }

    /// <summary>
    /// Determines if the model supports the specified test mode
    /// </summary>
    public static bool SupportsTestMode(this TDA4Model model, TestMode testMode)
    {
        return model.GetSupportedTestModes().Contains(testMode);
    }

    /// <summary>
    /// Determines if the model supports the specified core type
    /// </summary>
    public static bool SupportsCore(this TDA4Model model, CoreType coreType)
    {
        return model.GetSupportedCores().Contains(coreType);
    }
}
