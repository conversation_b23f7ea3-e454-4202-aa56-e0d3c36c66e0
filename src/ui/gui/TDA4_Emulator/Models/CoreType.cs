using System.Collections.Generic;

namespace TDA4_Emulator.Models;

/// <summary>
/// Represents the different processor cores in the TDA4 emulator (14 cores total)
/// </summary>
public enum CoreType
{
    // ARM A72 Cores (2 cores)
    /// <summary>
    /// A72-VM0 (ARM Cortex-A72 Virtual Machine 0)
    /// </summary>
    A72_VM0,

    /// <summary>
    /// A72-VM1 (ARM Cortex-A72 Virtual Machine 1)
    /// </summary>
    A72_VM1,

    // ARM MCU R5F Cores (6 cores)
    /// <summary>
    /// MCU-R5F-0 (ARM Cortex-R5F MCU Core 0)
    /// </summary>
    MCU_R5F_0,

    /// <summary>
    /// MCU-R5F-1 (ARM Cortex-R5F MCU Core 1)
    /// </summary>
    MCU_R5F_1,

    /// <summary>
    /// MCU-R5F-2 (ARM Cortex-R5F MCU Core 2)
    /// </summary>
    MCU_R5F_2,

    /// <summary>
    /// MCU-R5F-3 (ARM Cortex-R5F MCU Core 3)
    /// </summary>
    MCU_R5F_3,

    /// <summary>
    /// MCU-R5F-4 (ARM Cortex-R5F MCU Core 4)
    /// </summary>
    MCU_R5F_4,

    /// <summary>
    /// MCU-R5F-5 (ARM Cortex-R5F MCU Core 5)
    /// </summary>
    MCU_R5F_5,

    // ARM Main R5F Cores (6 cores)
    /// <summary>
    /// Main-R5F-0 (ARM Cortex-R5F Main Core 0)
    /// </summary>
    Main_R5F_0,

    /// <summary>
    /// Main-R5F-1 (ARM Cortex-R5F Main Core 1)
    /// </summary>
    Main_R5F_1,

    /// <summary>
    /// Main-R5F-2 (ARM Cortex-R5F Main Core 2)
    /// </summary>
    Main_R5F_2,

    /// <summary>
    /// Main-R5F-3 (ARM Cortex-R5F Main Core 3)
    /// </summary>
    Main_R5F_3,

    /// <summary>
    /// Main-R5F-4 (ARM Cortex-R5F Main Core 4)
    /// </summary>
    Main_R5F_4,

    /// <summary>
    /// Main-R5F-5 (ARM Cortex-R5F Main Core 5)
    /// </summary>
    Main_R5F_5,

    // DSP C7x Cores (4 cores)
    /// <summary>
    /// C7x-0 (TI C7x DSP Core 0)
    /// </summary>
    C7x_0,

    /// <summary>
    /// C7x-1 (TI C7x DSP Core 1)
    /// </summary>
    C7x_1,

    /// <summary>
    /// C7x-2 (TI C7x DSP Core 2)
    /// </summary>
    C7x_2,

    /// <summary>
    /// C7x-3 (TI C7x DSP Core 3)
    /// </summary>
    C7x_3,

    /// <summary>
    /// All cores (used for broadcast operations)
    /// </summary>
    All,

    // Legacy core types for backward compatibility
    /// <summary>
    /// Legacy R5F core type (maps to MCU_R5F_0)
    /// </summary>
    R5F,

    /// <summary>
    /// Legacy A72 core type (maps to A72_VM0)
    /// </summary>
    A72,

    /// <summary>
    /// Legacy C7x core type (maps to C7x_0)
    /// </summary>
    C7x
}

/// <summary>
/// Extension methods for CoreType enum
/// </summary>
public static class CoreTypeExtensions
{
    /// <summary>
    /// Gets the display name for the core type
    /// </summary>
    public static string GetDisplayName(this CoreType coreType)
    {
        return coreType switch
        {
            // ARM A72 Cores
            CoreType.A72_VM0 => "A72-VM0",
            CoreType.A72_VM1 => "A72-VM1",

            // ARM MCU R5F Cores
            CoreType.MCU_R5F_0 => "MCU-R5F-0",
            CoreType.MCU_R5F_1 => "MCU-R5F-1",
            CoreType.MCU_R5F_2 => "MCU-R5F-2",
            CoreType.MCU_R5F_3 => "MCU-R5F-3",
            CoreType.MCU_R5F_4 => "MCU-R5F-4",
            CoreType.MCU_R5F_5 => "MCU-R5F-5",

            // ARM Main R5F Cores
            CoreType.Main_R5F_0 => "Main-R5F-0",
            CoreType.Main_R5F_1 => "Main-R5F-1",
            CoreType.Main_R5F_2 => "Main-R5F-2",
            CoreType.Main_R5F_3 => "Main-R5F-3",
            CoreType.Main_R5F_4 => "Main-R5F-4",
            CoreType.Main_R5F_5 => "Main-R5F-5",

            // DSP C7x Cores
            CoreType.C7x_0 => "C7x-0",
            CoreType.C7x_1 => "C7x-1",
            CoreType.C7x_2 => "C7x-2",
            CoreType.C7x_3 => "C7x-3",

            // Special types
            CoreType.All => "All Cores",

            // Legacy types
            CoreType.R5F => "R5F Core",
            CoreType.A72 => "A72 Core",
            CoreType.C7x => "C7x Core",

            _ => coreType.ToString()
        };
    }

    /// <summary>
    /// Gets the short name for the core type (used in logging)
    /// </summary>
    public static string GetShortName(this CoreType coreType)
    {
        return coreType switch
        {
            // ARM A72 Cores
            CoreType.A72_VM0 => "A72-VM0",
            CoreType.A72_VM1 => "A72-VM1",

            // ARM MCU R5F Cores
            CoreType.MCU_R5F_0 => "MCU-R5F-0",
            CoreType.MCU_R5F_1 => "MCU-R5F-1",
            CoreType.MCU_R5F_2 => "MCU-R5F-2",
            CoreType.MCU_R5F_3 => "MCU-R5F-3",
            CoreType.MCU_R5F_4 => "MCU-R5F-4",
            CoreType.MCU_R5F_5 => "MCU-R5F-5",

            // ARM Main R5F Cores
            CoreType.Main_R5F_0 => "Main-R5F-0",
            CoreType.Main_R5F_1 => "Main-R5F-1",
            CoreType.Main_R5F_2 => "Main-R5F-2",
            CoreType.Main_R5F_3 => "Main-R5F-3",
            CoreType.Main_R5F_4 => "Main-R5F-4",
            CoreType.Main_R5F_5 => "Main-R5F-5",

            // DSP C7x Cores
            CoreType.C7x_0 => "C7x-0",
            CoreType.C7x_1 => "C7x-1",
            CoreType.C7x_2 => "C7x-2",
            CoreType.C7x_3 => "C7x-3",

            // Special types
            CoreType.All => "ALL",

            // Legacy types
            CoreType.R5F => "R5F",
            CoreType.A72 => "A72",
            CoreType.C7x => "C7x",

            _ => coreType.ToString()
        };
    }

    /// <summary>
    /// Gets all individual core types (excludes All and legacy types)
    /// </summary>
    public static IEnumerable<CoreType> GetIndividualCores()
    {
        return new[]
        {
            // ARM A72 Cores
            CoreType.A72_VM0, CoreType.A72_VM1,

            // ARM MCU R5F Cores
            CoreType.MCU_R5F_0, CoreType.MCU_R5F_1, CoreType.MCU_R5F_2,
            CoreType.MCU_R5F_3, CoreType.MCU_R5F_4, CoreType.MCU_R5F_5,

            // ARM Main R5F Cores
            CoreType.Main_R5F_0, CoreType.Main_R5F_1, CoreType.Main_R5F_2,
            CoreType.Main_R5F_3, CoreType.Main_R5F_4, CoreType.Main_R5F_5,

            // DSP C7x Cores
            CoreType.C7x_0, CoreType.C7x_1, CoreType.C7x_2, CoreType.C7x_3
        };
    }

    /// <summary>
    /// Gets ARM A72 core types
    /// </summary>
    public static IEnumerable<CoreType> GetA72Cores()
    {
        return new[] { CoreType.A72_VM0, CoreType.A72_VM1 };
    }

    /// <summary>
    /// Gets ARM MCU R5F core types
    /// </summary>
    public static IEnumerable<CoreType> GetMcuR5FCores()
    {
        return new[]
        {
            CoreType.MCU_R5F_0, CoreType.MCU_R5F_1, CoreType.MCU_R5F_2,
            CoreType.MCU_R5F_3, CoreType.MCU_R5F_4, CoreType.MCU_R5F_5
        };
    }

    /// <summary>
    /// Gets ARM Main R5F core types
    /// </summary>
    public static IEnumerable<CoreType> GetMainR5FCores()
    {
        return new[]
        {
            CoreType.Main_R5F_0, CoreType.Main_R5F_1, CoreType.Main_R5F_2,
            CoreType.Main_R5F_3, CoreType.Main_R5F_4, CoreType.Main_R5F_5
        };
    }

    /// <summary>
    /// Gets DSP C7x core types
    /// </summary>
    public static IEnumerable<CoreType> GetC7xCores()
    {
        return new[] { CoreType.C7x_0, CoreType.C7x_1, CoreType.C7x_2, CoreType.C7x_3 };
    }

    /// <summary>
    /// Gets the core group name for grouping in UI
    /// </summary>
    public static string GetCoreGroup(this CoreType coreType)
    {
        return coreType switch
        {
            CoreType.A72_VM0 or CoreType.A72_VM1 => "ARM A72",
            CoreType.MCU_R5F_0 or CoreType.MCU_R5F_1 or CoreType.MCU_R5F_2 or
            CoreType.MCU_R5F_3 or CoreType.MCU_R5F_4 or CoreType.MCU_R5F_5 => "ARM MCU R5F",
            CoreType.Main_R5F_0 or CoreType.Main_R5F_1 or CoreType.Main_R5F_2 or
            CoreType.Main_R5F_3 or CoreType.Main_R5F_4 or CoreType.Main_R5F_5 => "ARM Main R5F",
            CoreType.C7x_0 or CoreType.C7x_1 or CoreType.C7x_2 or CoreType.C7x_3 => "DSP C7x",
            _ => "Other"
        };
    }
}
